"""
Client for interacting with the Alibaba Cloud AI models API.
Supports multiple models including DeepSeek R1 and 通义千问-Plus-Latest.
"""
import json
import requests
import logging
import time
import hashlib
import re
import traceback
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

# 使用相对导入避免循环导入问题
import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import config
from src.db.connection import Session
from src.models.api_log import ApiLog
from src.models.intermediate_result import IntermediateResult

logger = logging.getLogger(__name__)

# 全局客户端实例，用于缓存
_client_instance = None

class DeepSeekClient:
    """Client for interacting with the Alibaba Cloud AI models API."""

    # 类级别变量，用于跟踪API调用
    _api_calls = []
    _api_call_limit = 10000  # 设置为较大值，实际上不限制
    _api_call_window = 3600  # 时间窗口（秒）

    # 类级别变量，用于跟踪API费用
    _total_tokens_used = 0
    _total_cost = 0.0
    _session_start_time = time.time()  # 初始化为程序启动时间
    _analysis_start_time = time.time()  # 当前分析开始时间

    def __init__(self, model: Optional[str] = None, api_key: Optional[str] = None, api_base_url: Optional[str] = None):
        """
        Initialize the Alibaba Cloud AI models API client.

        Args:
            model: Model to use. If None, uses the default model from config.
            api_key: API key. If None, uses the key from config based on the model.
            api_base_url: API base URL. If None, uses the URL from config.
        """
        # 设置当前使用的模型
        self.model = model or config.DEFAULT_MODEL

        # 🔧 修复：根据模型配置选择正确的API密钥和端点
        self.model_config = config.SUPPORTED_MODELS.get(self.model, {})
        self.provider = self.model_config.get("provider", "aliyun")

        if api_key:
            self.api_key = api_key
        else:
            # 从模型配置中获取API密钥
            self.api_key = self.model_config.get("api_key", config.DEEPSEEK_API_KEY)

        # 🔧 修复：根据提供商设置正确的API基础URL和端点
        if self.provider == "deepseek_official":
            # 官方DeepSeek API
            self.api_base_url = api_base_url or config.DEEPSEEK_OFFICIAL_API_BASE_URL
            self.endpoint = self.model_config.get("endpoint", f"{self.api_base_url}/v1/chat/completions")
        else:
            # 阿里云API（默认）
            self.api_base_url = api_base_url or config.DASHSCOPE_API_BASE_URL
            self.endpoint = self.model_config.get("endpoint", f"{self.api_base_url}/services/aigc/text-generation/generation")

        self.model_name = self.model_config.get("name", self.model)

        logger.info(f"初始化 {self.model_name} API 客户端, API基础URL: {self.api_base_url}")
        logger.info(f"使用模型: {self.model_name}, 端点: {self.endpoint}")

        # 当前正在分析的小说ID
        self.current_novel_id = None
        self.current_analysis_type = None

        # 打印API密钥前几个字符（安全起见不打印全部）
        if self.api_key:
            masked_key = self.api_key[:6] + "..." + self.api_key[-4:]
            logger.info(f"API密钥已配置: {masked_key}")
        else:
            logger.warning("API密钥未配置，请检查环境变量或配置文件")

        # 从配置中获取API调用限制 - 已禁用限制
        self._api_call_limit = getattr(config, 'API_CALL_LIMIT_PER_HOUR', 10000)
        logger.info(f"API调用限制已禁用，改为记录调用次数和费用")

    @classmethod
    def _record_api_call(cls, input_tokens=0, output_tokens=0):
        """
        记录API调用，并清理过期记录，同时计算费用

        Args:
            input_tokens: 输入令牌数
            output_tokens: 输出令牌数
        """
        current_time = time.time()
        # 添加当前调用
        cls._api_calls.append(current_time)
        # 清理超过时间窗口的调用记录
        cls._api_calls = [t for t in cls._api_calls if current_time - t < cls._api_call_window]

        # 计算并累加令牌使用量和费用
        if input_tokens > 0 or output_tokens > 0:
            total_tokens = input_tokens + output_tokens
            cls._total_tokens_used += total_tokens

            # 计算费用（元）
            input_cost = (input_tokens / 1000) * config.API_COST_INPUT_PER_1K_TOKENS
            output_cost = (output_tokens / 1000) * config.API_COST_OUTPUT_PER_1K_TOKENS
            total_cost = input_cost + output_cost

            cls._total_cost += total_cost

            # 记录到日志
            logger.info(f"[API费用] 本次调用: 输入={input_tokens}令牌, 输出={output_tokens}令牌, 总计={total_tokens}令牌")
            logger.info(f"[API费用] 本次费用: 输入={input_cost:.4f}元, 输出={output_cost:.4f}元, 总计={total_cost:.4f}元")
            logger.info(f"[API费用] 累计使用: {cls._total_tokens_used}令牌, 累计费用: {cls._total_cost:.4f}元")

            return {
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": total_tokens,
                "input_cost": input_cost,
                "output_cost": output_cost,
                "total_cost": total_cost
            }

        return None

    @classmethod
    def get_api_call_stats(cls):
        """获取API调用统计信息，包括令牌使用量和费用"""
        current_time = time.time()
        # 清理过期记录
        cls._api_calls = [t for t in cls._api_calls if current_time - t < cls._api_call_window]
        # 计算当前小时内的调用次数
        calls_in_window = len(cls._api_calls)

        # 计算会话时长（分钟）
        session_duration_seconds = current_time - cls._session_start_time
        session_duration_minutes = session_duration_seconds / 60

        # 确保会话时长是合理的值（不超过24小时）
        if session_duration_minutes > 24 * 60:  # 超过24小时
            logger.warning(f"检测到异常会话时长: {session_duration_minutes:.2f}分钟，重置为当前分析时长")
            # 重置会话开始时间为分析开始时间
            cls._session_start_time = cls._analysis_start_time if hasattr(cls, '_analysis_start_time') and cls._analysis_start_time > 0 else current_time - 3600
            session_duration_seconds = current_time - cls._session_start_time
            session_duration_minutes = session_duration_seconds / 60

        # 计算当前分析时长（分钟）
        # 确保分析开始时间存在且有效
        if hasattr(cls, '_analysis_start_time') and cls._analysis_start_time > 0:
            analysis_duration_seconds = current_time - cls._analysis_start_time
            analysis_duration_minutes = analysis_duration_seconds / 60

            # 确保分析时长是合理的值（不超过会话时长）
            if analysis_duration_minutes > session_duration_minutes:
                logger.warning(f"检测到异常分析时长: {analysis_duration_minutes:.2f}分钟 > 会话时长: {session_duration_minutes:.2f}分钟，调整为会话时长")
                analysis_duration_minutes = session_duration_minutes

            # 确保分析时长不会过大（不超过12小时）
            if analysis_duration_minutes > 12 * 60:
                logger.warning(f"检测到异常分析时长: {analysis_duration_minutes:.2f}分钟，重置为合理值")
                # 重置分析开始时间
                cls._analysis_start_time = current_time - 3600  # 默认设为1小时前
                analysis_duration_seconds = current_time - cls._analysis_start_time
                analysis_duration_minutes = analysis_duration_seconds / 60
        else:
            # 如果分析开始时间无效，使用会话时长作为备用，但不超过1小时
            analysis_duration_minutes = min(session_duration_minutes, 60)
            # 设置分析开始时间为当前时间，避免未来计算出错
            cls._analysis_start_time = current_time - (analysis_duration_minutes * 60)

        # 记录时长信息到日志
        logger.info(f"会话时长: {session_duration_minutes:.2f}分钟 ({session_duration_seconds:.2f}秒)")
        logger.info(f"分析时长: {analysis_duration_minutes:.2f}分钟 ({analysis_duration_seconds:.2f}秒)")

        return {
            "calls_in_window": calls_in_window,
            "total_calls": len(cls._api_calls),
            "total_tokens": cls._total_tokens_used,
            "total_cost": cls._total_cost,
            "session_duration_minutes": round(session_duration_minutes, 2),
            "analysis_duration_minutes": round(analysis_duration_minutes, 2),
            "session_duration_seconds": round(session_duration_seconds, 2),
            "analysis_duration_seconds": round(analysis_duration_seconds, 2),
            "average_cost_per_call": round(cls._total_cost / max(1, len(cls._api_calls)), 4),
            "average_tokens_per_call": round(cls._total_tokens_used / max(1, len(cls._api_calls)), 0)
        }

    def _save_api_log(self, api_log):
        """保存API调用日志到数据库"""
        try:
            session = Session()

            # 确保API日志对象有效
            if not hasattr(api_log, 'endpoint') or not api_log.endpoint:
                api_log.endpoint = "unknown"
            if not hasattr(api_log, 'method') or not api_log.method:
                api_log.method = "POST"

            # 添加并保存
            session.add(api_log)
            session.commit()
            logger.debug(f"已保存API调用日志，ID: {api_log.id}")
        except Exception as e:
            logger.error(f"保存API调用日志时出错: {str(e)}")
            try:
                session.rollback()
            except Exception as rollback_error:
                logger.error(f"回滚API日志保存时出错: {str(rollback_error)}")
        finally:
            try:
                session.close()
            except Exception as close_error:
                logger.error(f"关闭数据库会话时出错: {str(close_error)}")

    @classmethod
    def check_rate_limit(cls):
        """
        检查API调用统计信息，但不限制调用

        Returns:
            (True, stats): 始终返回True表示允许调用，同时返回统计信息
        """
        stats = cls.get_api_call_stats()
        # 已禁用API调用限制，始终返回True
        return True, stats

    def analyze_text(self, text: str, analysis_type: str, novel_id: int = None, chapter_id: int = None, progress_callback=None, max_tokens: int = None, prompt_template: str = "default", temperature: float = None, stream: bool = False) -> Dict[str, Any]:
        """
        Analyze text using the DeepSeek API with dynamic max_tokens based on analysis type and text length.

        Args:
            text: Text to analyze.
            analysis_type: Type of analysis to perform.
            novel_id: Novel ID (optional).
            chapter_id: Chapter ID (optional).
            progress_callback: Progress callback function (optional).
            max_tokens: Optional override for max_tokens. If None, will be determined dynamically.
            prompt_template: Prompt template to use (default or simplified).
            temperature: Optional temperature parameter for controlling randomness.
            stream: Whether to use streaming output for large content analysis (recommended for book analysis).

        Returns:
            Analysis result.
        """
        # 记录当前分析的小说ID和章节ID（使用简单的整数值，避免存储对象引用）
        self.current_novel_id = novel_id
        self.current_chapter_id = chapter_id

        # 记录实际使用的小说ID和章节ID，用于调试
        logger.info(f"实际使用的小说ID: {novel_id}, 章节ID: {chapter_id}")
        # 动态确定max_tokens - 使用智能优化配置
        if max_tokens is None:
            try:
                from src.config.token_optimization_config import TokenOptimizationConfig

                # 使用智能token优化
                max_tokens = TokenOptimizationConfig.get_optimized_max_tokens(
                    dimension=analysis_type,
                    prompt_template=prompt_template or "default",
                    text_length=len(text)
                )

                # 记录优化效果
                if hasattr(config, 'DIMENSION_MAX_TOKENS'):
                    original_tokens = config.DIMENSION_MAX_TOKENS.get(analysis_type, config.DIMENSION_MAX_TOKENS.get("default", 15000))
                    savings = TokenOptimizationConfig.calculate_token_savings(original_tokens, max_tokens)
                    logger.info(f"[Token优化] 维度:{analysis_type}, 原始:{original_tokens} → 优化:{max_tokens}")
                    logger.info(f"[Token优化] 节省率:{savings['savings_rate']:.1%}, 预计节省成本:{savings['cost_savings']:.4f}元")

            except ImportError:
                # 降级到原有逻辑
                logger.warning("Token优化配置不可用，使用原有逻辑")
                if hasattr(config, 'DIMENSION_MAX_TOKENS'):
                    max_tokens = config.DIMENSION_MAX_TOKENS.get(analysis_type, config.DIMENSION_MAX_TOKENS.get("default", 2000))
                else:
                    max_tokens = 2000  # 默认值

            # 精简版提示词模式下，只对分析功能进行激进优化，写作功能保持不变
            if prompt_template == "simplified":
                # 检查是否为写作相关功能，如果是则不进行激进优化
                writing_analysis_types = [
                    "chapter_content_generation", "chapter_framework", "chapter_generation",
                    "content_generation", "writing", "story_generation", "novel_generation"
                ]

                if analysis_type in writing_analysis_types:
                    # 写作功能不进行激进优化，保持原有质量
                    logger.info(f"[写作功能保护] 精简版模式：{analysis_type}为写作功能，不进行激进优化，保持原有max_tokens={max_tokens}")
                else:
                    # 只对分析功能进行激进优化
                    if analysis_type in ["language_style", "rhythm_pacing", "structure"]:
                        # 核心分析维度更激进优化，减少75%
                        max_tokens = int(max_tokens * 0.25)
                        logger.info(f"[激进降本增效-分析] 精简版模式：核心分析维度{analysis_type}，max_tokens减少75%至{max_tokens}（激进降低分析成本）")
                    elif analysis_type in ["paragraph_length", "sentence_variation", "perspective_shifts"]:
                        # 次要分析维度极度减少，减少85%
                        max_tokens = int(max_tokens * 0.15)
                        logger.info(f"[激进降本增效-分析] 精简版模式：次要分析维度{analysis_type}，max_tokens减少85%至{max_tokens}（极度降低分析成本）")
                    elif analysis_type in ["chapter_outline", "outline_analysis"]:
                        # 章纲分析维度适度激进，减少70%
                        max_tokens = int(max_tokens * 0.3)
                        logger.info(f"[激进降本增效-分析] 精简版模式：章纲分析维度{analysis_type}，max_tokens减少70%至{max_tokens}（保持分析必要详细度）")
                    else:
                        # 其他分析维度激进减少，减少80%
                        max_tokens = int(max_tokens * 0.2)
                        logger.info(f"[激进降本增效-分析] 精简版模式：常规分析维度{analysis_type}，max_tokens减少80%至{max_tokens}（激进降低分析成本）")

            # 根据文本长度动态调整max_tokens
            text_length = len(text)
            if text_length < 2000:
                # 对于短文本，适当减少max_tokens
                max_tokens = int(min(max_tokens, max_tokens * 0.8))
            elif text_length > 10000:
                # 对于长文本，适当增加max_tokens，但不超过配置的1.2倍
                if prompt_template == "simplified":
                    # 精简版模式下，即使是长文本也不增加太多token
                    max_tokens = int(min(max_tokens * 1.1, max_tokens * 1.1))
                else:
                    max_tokens = int(min(max_tokens * 1.2, max_tokens * 1.2))

            logger.info(f"[API请求] 为 {analysis_type} 分析动态设置max_tokens: {max_tokens}，文本长度: {text_length}，提示词模板: {prompt_template}")

        # 根据模型限制调整max_tokens
        # 获取模型配置中的max_tokens限制
        model_config = config.SUPPORTED_MODELS.get(self.model, {})
        model_max_tokens = model_config.get("max_tokens", 12000)  # 默认值为12000

        # 确保max_tokens不超过模型限制
        if max_tokens > model_max_tokens:
            logger.warning(f"[API请求] max_tokens值 {max_tokens} 超过模型 {self.model} 的限制 {model_max_tokens}，已调整为 {model_max_tokens}")
            max_tokens = model_max_tokens

        # 设置当前分析类型
        self.current_analysis_type = analysis_type

        # 精简版模式下，实施多种降本增效策略
        if prompt_template == "simplified":
            # 策略1：智能缓存（降本增效方案4）
            cache_key = self._generate_cache_key(text, analysis_type, prompt_template)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                logger.info(f"[新降本增效] 精简版模式：命中缓存，跳过API调用，节省成本")
                return cached_result

            # 策略2：结果复用检查（新降本增效方案4）
            if analysis_type in ["paragraph_length", "sentence_variation"]:
                similar_result = self._check_similar_analysis_result(text, analysis_type)
                if similar_result:
                    logger.info(f"[新降本增效] 精简版模式：找到相似分析结果，复用并适配，节省{len(text)}字符的分析成本")
                    adapted_result = self._adapt_similar_result(similar_result, text, analysis_type)
                    # 保存到缓存
                    self._save_cached_result(cache_key, adapted_result)
                    return adapted_result

            # 新增策略9：智能维度合并分析（超级降本增效）
            if analysis_type in ["language_style", "rhythm_pacing", "structure"]:
                merged_result = self._try_merged_dimension_analysis(text, analysis_type, prompt_template)
                if merged_result:
                    logger.info(f"[超级降本增效] 精简版模式：使用维度合并分析，一次API调用获得多个维度结果，节省{len(text)*2}字符的分析成本")
                    return merged_result

            # 新增策略10：动态质量阈值调整
            quality_threshold = self._calculate_quality_threshold(text, analysis_type)
            if quality_threshold < 0.7:  # 对于简单文本，使用更激进的优化
                max_tokens = int(max_tokens * 0.6)  # 进一步减少40%
                logger.info(f"[超级降本增效] 检测到简单文本，质量阈值{quality_threshold:.2f}，max_tokens进一步减少40%至{max_tokens}")

            # 新增策略11：智能分段优化
            if len(text) > 5000:
                optimized_segments = self._optimize_text_segments(text, analysis_type)
                if len(optimized_segments) < len(text):
                    text = optimized_segments
                    logger.info(f"[超级降本增效] 智能分段优化：文本从{len(text)}字符优化到{len(optimized_segments)}字符，减少{((len(text)-len(optimized_segments))/len(text)*100):.1f}%")

        # 根据提示词模板显示不同的日志信息
        if prompt_template == "simplified":
            logger.info(f"[API请求] 🚀 开始 {analysis_type} 分析（精简版降本增效模式），文本长度: {len(text)} 字符")
            logger.info(f"[API请求] 💰 最大生成令牌数: {max_tokens}（已优化降低40%）")
        else:
            logger.info(f"[API请求] 开始 {analysis_type} 分析（默认版），文本长度: {len(text)} 字符")
            logger.info(f"[API请求] 最大生成令牌数: {max_tokens}")

        # 尝试添加分析日志到控制台
        try:
            # 尝试获取当前正在分析的小说ID
            novel_id = getattr(self, 'current_novel_id', None)
            if novel_id:
                # 使用日志记录而不是直接调用add_analysis_log
                logger.info(f"[API请求] 开始 {analysis_type} 分析，文本长度: {len(text)} 字符")
                logger.debug(f"[API请求] 最大生成令牌数: {max_tokens}")

                # 获取当前API调用统计
                stats = self.get_api_call_stats()
                logger.info(f"[API统计] 当前会话: 已调用{stats['total_calls']}次API, 累计费用: {stats['total_cost']:.4f}元")
        except Exception as log_error:
            logger.error(f"添加API分析日志时出错: {str(log_error)}")

        # 根据分析类型和提示词模板构建提示词
        prompt = self._create_analysis_prompt(text, analysis_type, prompt_template)

        # 检查提示词是否为空，避免API参数错误
        if not prompt or prompt.strip() == "":
            logger.error(f"[API请求] 提示词为空，分析类型: {analysis_type}, 文本长度: {len(text) if text else 0}")
            return {
                "error": f"提示词构建失败：分析类型 {analysis_type} 的提示词为空",
                "type": analysis_type
            }

        # 精简版模式下，只对分析功能实施智能文本分块和提示词压缩，写作功能保持不变
        if prompt_template == "simplified":
            # 检查是否为写作相关功能，如果是则不进行文本压缩
            writing_analysis_types = [
                "chapter_content_generation", "chapter_framework", "chapter_generation",
                "content_generation", "writing", "story_generation", "novel_generation"
            ]

            if analysis_type in writing_analysis_types:
                # 写作功能不进行文本压缩，保持原有质量
                logger.info(f"[写作功能保护] 精简版模式：{analysis_type}为写作功能，不进行文本压缩，保持原有质量")
            elif len(text) > 20000:  # 只对分析功能的长文本进行分块处理
                # 智能文本压缩（仅限分析功能）
                compressed_text = self._compress_text_intelligently(text, analysis_type)
                logger.info(f"[新降本增效-分析] 精简版模式：分析功能智能文本压缩，从{len(text)}字符压缩到{len(compressed_text)}字符，压缩率{(1-len(compressed_text)/len(text))*100:.1f}%")
                text = compressed_text

            # 重新构建提示词（因为text可能已经被压缩）
            prompt = self._create_analysis_prompt(text, analysis_type, prompt_template)

            # 只对分析功能进行智能提示词压缩，写作功能保持不变
            if analysis_type in writing_analysis_types:
                # 写作功能不进行提示词压缩，保持原有质量
                logger.info(f"[写作功能保护] 精简版模式：{analysis_type}为写作功能，不进行提示词压缩，保持原有质量")
            else:
                # 智能提示词压缩（仅限分析功能）
                original_prompt_length = len(prompt)
                prompt = self._compress_prompt_intelligently(prompt, analysis_type, max_tokens, prompt_template)
                compressed_length = len(prompt)

                # 避免除零错误
                if original_prompt_length > 0:
                    compression_ratio = (original_prompt_length - compressed_length) / original_prompt_length * 100
                    logger.info(f"[新降本增效-分析] 智能提示词压缩：原长度{original_prompt_length}字符，压缩后{compressed_length}字符，压缩率{compression_ratio:.1f}%")
                else:
                    logger.warning(f"[新降本增效-分析] 智能提示词压缩：原始提示词长度为0，无法计算压缩率")
                    logger.info(f"[新降本增效-分析] 智能提示词压缩：原长度{original_prompt_length}字符，压缩后{compressed_length}字符")

            # 只对分析功能应用额外的降本增效策略，写作功能保持不变
            if analysis_type not in writing_analysis_types:
                # 新增策略12：动态停止词优化（仅限分析功能）
                if analysis_type in ["language_style", "rhythm_pacing"]:
                    # 为特定分析类型添加停止词，减少不必要的输出
                    stop_words = self._get_analysis_stop_words(analysis_type)
                    if stop_words:
                        logger.info(f"[超级降本增效-分析] 为{analysis_type}分析添加{len(stop_words)}个停止词，减少冗余输出")

                # 新增策略13：智能token预估与调整（仅限分析功能）
                estimated_input_tokens = len(prompt) // 4  # 粗略估计
                if estimated_input_tokens > 1000:
                    # 对于大输入，进一步减少输出token
                    max_tokens = int(max_tokens * 0.8)
                    logger.info(f"[超级降本增效-分析] 检测到大输入({estimated_input_tokens}tokens)，输出token减少20%至{max_tokens}")

                # 新增策略14：分析复杂度自适应（仅限分析功能）
                complexity_score = self._calculate_analysis_complexity(text, analysis_type)
                if complexity_score < 0.5:  # 简单分析
                    max_tokens = int(max_tokens * 0.7)
                    logger.info(f"[超级降本增效-分析] 检测到简单分析(复杂度{complexity_score:.2f})，输出token减少30%至{max_tokens}")
            else:
                # 写作功能不应用额外的降本增效策略
                logger.info(f"[写作功能保护] 精简版模式：{analysis_type}为写作功能，不应用额外的降本增效策略")

        # 使用当前模型的端点
        endpoint = self.endpoint
        logger.info(f"[API请求] 使用 {self.model_name} API端点: {endpoint}")

        # 打印API密钥信息
        logger.info(f"[API请求] 使用的API密钥: {self.api_key[:6]}...")

        start_time = time.time()
        logger.info(f"[API请求] 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 如果是API测试连接，使用示范样本，但强制禁用DEBUG模式
        if analysis_type == "test_connection":
            logger.info(f"[API请求] 使用示范样本响应，分析类型: {analysis_type}")
            return self._get_sample_response(analysis_type, text)

        # 强制禁用DEBUG模式，确保使用真实API调用
        if False and config.DEBUG:  # 永远不会执行的条件
            logger.info(f"[API请求] DEBUG模式已被强制禁用，将使用真实API调用")
            # 不返回示范样本，继续执行真实API调用

        # 检查API调用频率限制
        if config.API_RATE_LIMIT_ENABLED:
            can_call, stats = self.check_rate_limit()
            if not can_call:
                # 精简版模式下，实施请求优先级管理（降本增效方案8）
                if prompt_template == "simplified":
                    # 精简版模式下，等待时间更短，优先处理
                    wait_time = min(stats['reset_seconds'], 30)  # 最多等待30秒
                    logger.warning(f"[降本增效] 精简版模式：API调用频率超出限制，优先处理，等待时间缩短至{wait_time}秒")
                    if wait_time <= 10:  # 如果等待时间很短，直接等待
                        time.sleep(wait_time)
                        logger.info(f"[降本增效] 精简版模式：等待完成，继续处理请求")
                    else:
                        return {
                            "error": f"精简版模式：API调用频率超出限制，请等待 {wait_time} 秒后重试"
                        }
                else:
                    logger.warning(f"API调用频率超出限制! 已调用: {stats['calls_in_window']}/{stats['limit']}，将在 {stats['reset_seconds']} 秒后重置")
                    return {
                        "error": f"API调用频率超出限制，请稍后再试。已调用: {stats['calls_in_window']}/{stats['limit']}，将在 {stats['reset_seconds']} 秒后重置"
                    }

        # 添加请求状态监控
        request_start_time = time.time()
        request_id = f"req_{int(request_start_time)}_{analysis_type}"

        # 记录请求开始时间
        logger.info(f"[API请求] 开始请求 ID: {request_id}, 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 实际API调用
        try:
            # 创建API日志记录
            try:
                api_log = ApiLog(
                    endpoint="text-generation",
                    method="POST",
                    novel_id=getattr(self, 'current_novel_id', None),
                    analysis_type=analysis_type
                )
                # 记录请求参数
                api_log.input_tokens = len(text) // 4  # 粗略估计输入令牌数
            except Exception as e:
                logger.error(f"创建API日志记录时出错: {str(e)}")
                api_log = ApiLog(
                    endpoint="text-generation",
                    method="POST"
                )

            # 确保API密钥格式正确
            # 阿里云DeepSeek API需要使用Bearer前缀的认证头
            api_key = self.api_key

            # 确保使用最新的API密钥
            if self.model == "deepseek-r1":
                api_key = config.DEEPSEEK_API_KEY
                logger.info(f"[API请求] 使用DeepSeek R1 (阿里云) API密钥: {api_key[:6]}...")
            elif self.model == "deepseek-r1-0528-official":
                api_key = config.DEEPSEEK_OFFICIAL_API_KEY
                logger.info(f"[API请求] 使用DeepSeek R1-0528 (官方) API密钥: {api_key[:6]}...")
            elif self.model == "qwen-plus-latest":
                api_key = config.QWEN_API_KEY
                logger.info(f"[API请求] 使用通义千问-Plus-Latest API密钥: {api_key[:6]}...")
            elif self.model == "qwen-qvq-max":
                api_key = config.QWEN_QVQ_API_KEY
                logger.info(f"[API请求] 使用通义千问-QVQ-Max API密钥: {api_key[:6]}...")

            # 移除可能存在的Bearer前缀，稍后会重新添加
            if api_key.startswith("Bearer "):
                api_key = api_key[7:]  # 移除"Bearer "前缀

            # 阿里云DeepSeek API需要使用特定的认证头格式
            headers = {
                "Authorization": f"Bearer {api_key}",  # 阿里云DeepSeek API需要Bearer前缀
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            # 打印API密钥信息用于调试（不打印完整密钥，只打印前后几位）
            logger.info(f"[API请求] 使用API密钥: {api_key[:6]}...{api_key[-4:] if len(api_key) > 10 else ''}")

            # 添加到控制台日志
            try:
                novel_id = getattr(self, 'current_novel_id', None)
                if novel_id:
                    # 使用日志记录而不是直接调用add_analysis_log
                    logger.info(f"[API请求] 准备调用阿里云 {self.model_name} API，使用密钥: {api_key[:6]}...{api_key[-4:]}")
                    logger.info(f"[API请求] 分析维度: {analysis_type}，文本长度: {len(text)} 字符")
                    logger.info(f"[API请求] 使用模型: {self.model}")
            except Exception as log_error:
                logger.error(f"添加API密钥日志时出错: {str(log_error)}")

            # 确保请求体中不包含任何可能导致编码问题的字符
            # 根据阿里云API的要求调整请求体格式
            # 参考阿里云API文档: https://help.aliyun.com/document_detail/2400162.html

            # 再次检查prompt是否为空，避免API参数错误
            if not prompt or prompt.strip() == "":
                logger.error(f"[API请求] 构建请求体时发现提示词为空，分析类型: {analysis_type}")
                return {
                    "error": f"API请求参数错误：提示词为空",
                    "type": analysis_type
                }

            # 根据模型选择不同的请求体格式
            if self.model == "deepseek-r1":
                # DeepSeek R1 (阿里云) 模型使用的请求体格式
                payload = {
                    "model": "deepseek-r1",
                    "input": {
                        "prompt": prompt,
                        "parameters": {
                            "max_tokens": max_tokens,
                            "temperature": 0.1 if temperature is None else temperature,
                            "top_p": 0.8,
                            "top_k": 50,
                            "repetition_penalty": 1.1,
                            "stop": []
                        }
                    }
                }
            elif self.model == "deepseek-r1-0528-official":
                # DeepSeek R1-0528 (官方) 模型使用的请求体格式
                # 根据任务类型决定是否启用深度思考
                # 只在写作任务中启用深度思考模式，分析任务不启用以节省成本
                writing_tasks = ["writing", "generation", "content_generation", "chapter_writing", "story_writing"]
                enable_reasoning = analysis_type in writing_tasks

                # 🔧 修复：使用正确的模型名称
                official_model_name = self.model_config.get("model_name", "deepseek-reasoner")

                payload = {
                    "model": official_model_name,  # 🔧 修复：使用配置中的正确模型名称
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": min(max_tokens, 64000)  # 🔧 修复：deepseek-reasoner最大支持64K输出
                    # 🔧 修复：deepseek-reasoner不支持temperature、top_p、frequency_penalty、presence_penalty参数
                }

                # 🔧 修复：deepseek-reasoner模型自动启用推理模式，无需额外参数
                logger.info(f"[API请求] 官方DeepSeek R1-0528：使用推理模型，自动启用深度思考")

                logger.info(f"[API请求] 参数: model={official_model_name}, max_tokens={min(max_tokens, 64000)}")

                # 🔧 修复：DeepSeek官方API不支持temperature参数，跳过温度调整
                logger.info(f"[API请求] DeepSeek官方API不支持temperature参数，使用默认推理模式")
            elif self.model == "qwen-qvq-max":
                # 通义千问-QVQ-Max 模型使用的请求体格式
                # 根据阿里云API文档，qwen-qvq-max 模型名称应该是 qwen-max
                # 再次确保prompt不为空
                if not prompt or prompt.strip() == "":
                    logger.error(f"[API请求] 通义千问-QVQ-Max模型：提示词为空")
                    return {
                        "error": f"API请求参数错误：通义千问-QVQ-Max模型的提示词为空",
                        "type": analysis_type
                    }

                payload = {
                    "model": "qwen-max",
                    "input": {
                        "messages": [
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ]
                    },
                    "parameters": {
                        "max_tokens": max_tokens,
                        "temperature": 0.7 if temperature is None else temperature,
                        "top_p": 0.8,
                        "result_format": "message"
                    }
                }

                # 只对分析功能使用较低的temperature，写作功能保持原有temperature
                if prompt_template == "simplified" and analysis_type not in writing_analysis_types:
                    # 精简版分析模式下使用更低的temperature，提高输出一致性，减少重复生成
                    original_temp = payload["parameters"]["temperature"]
                    payload["parameters"]["temperature"] = min(original_temp * 0.7, 0.5)
                    logger.info(f"[降本增效-分析] 精简版分析模式：temperature从{original_temp}降低至{payload['parameters']['temperature']}")
                elif prompt_template == "simplified" and analysis_type in writing_analysis_types:
                    # 写作功能保持原有temperature，确保创作质量
                    logger.info(f"[写作功能保护] 精简版模式：{analysis_type}为写作功能，保持原有temperature={payload['parameters']['temperature']}")
                logger.info(f"[API请求] 使用通义千问-QVQ-Max模型，实际API请求使用模型名: qwen-max")
            else:
                # 通义千问等其他模型使用的请求体格式
                # 再次确保prompt不为空
                if not prompt or prompt.strip() == "":
                    logger.error(f"[API请求] {self.model}模型：提示词为空")
                    return {
                        "error": f"API请求参数错误：{self.model}模型的提示词为空",
                        "type": analysis_type
                    }

                payload = {
                    "model": self.model,
                    "input": {
                        "messages": [
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ]
                    },
                    "parameters": {
                        "max_tokens": max_tokens,
                        "temperature": 0.7 if temperature is None else temperature,
                        "top_p": 0.8,
                        "result_format": "message"
                    }
                }

                # 只对分析功能使用较低的temperature，写作功能保持原有temperature
                if prompt_template == "simplified" and analysis_type not in writing_analysis_types:
                    # 精简版分析模式下使用更低的temperature，提高输出一致性，减少重复生成
                    original_temp = payload["parameters"]["temperature"]
                    payload["parameters"]["temperature"] = min(original_temp * 0.7, 0.5)
                    logger.info(f"[降本增效-分析] 精简版分析模式：temperature从{original_temp}降低至{payload['parameters']['temperature']}")
                elif prompt_template == "simplified" and analysis_type in writing_analysis_types:
                    # 写作功能保持原有temperature，确保创作质量
                    logger.info(f"[写作功能保护] 精简版模式：{analysis_type}为写作功能，保持原有temperature={payload['parameters']['temperature']}")

            logger.info(f"[API请求] 请求体: {payload}")

            # 根据模型选择正确的端点URL
            if self.model == "deepseek-r1-0528-official":
                # 官方模型使用官方API端点
                clean_endpoint = "https://api.deepseek.com/v1/chat/completions"
                logger.info(f"[API请求] 发送POST请求到DeepSeek官方API: {clean_endpoint}")
            else:
                # 其他模型使用阿里云端点
                clean_endpoint = self.endpoint
                logger.info(f"[API请求] 发送POST请求到阿里云 {self.model_name} API: {clean_endpoint}")
            logger.info(f"[API请求] 请求头: {headers}")
            logger.info(f"[API请求] 请求大小: {len(json.dumps(payload))} 字节")

            # 打印请求信息用于调试（不包含完整的请求头和请求体，避免泄露敏感信息）
            logger.info(f"[API请求] 请求信息: URL={clean_endpoint}, 请求体大小={len(json.dumps(payload))}字节")

            # 🔧 修复：根据提供商显示正确的日志信息
            provider_name = "DeepSeek官方" if self.provider == "deepseek_official" else "阿里云"

            # 添加到控制台日志
            try:
                novel_id = getattr(self, 'current_novel_id', None)
                if novel_id:
                    # 使用日志记录而不是直接调用add_analysis_log
                    logger.info(f"[API请求] 正在发送请求到{provider_name} {self.model_name} API...")
                    logger.debug(f"[API请求] 请求体大小: {len(json.dumps(payload))}字节")
                    logger.debug(f"[API请求] 分析类型: {analysis_type}")
            except Exception as log_error:
                logger.error(f"添加API请求日志时出错: {str(log_error)}")

            # 添加到控制台日志
            try:
                novel_id = getattr(self, 'current_novel_id', None)
                if novel_id:
                    # 使用日志记录而不是直接调用add_analysis_log
                    logger.info(f"[API请求] 发送请求到{provider_name} {self.model_name} API")
                    logger.debug(f"[API请求] 使用模型: {self.model}")
            except Exception as log_error:
                logger.error(f"添加API请求日志时出错: {str(log_error)}")

            # 添加增强的重试机制 - 针对超时错误优化重试策略
            # 根据模式优化重试策略，增强超时处理
            if prompt_template == "simplified":
                max_retries = 4  # 精简版适度减少重试次数，但保证稳定性
                base_retry_delay = 3  # 适中的基础延迟
                max_retry_delay = 45  # 适中的最大延迟
                logger.info(f"[重试优化] 精简版模式：重试次数{max_retries}次，延迟时间平衡效率与稳定性")
            else:
                max_retries = 6  # 默认版增加重试次数以应对超时
                base_retry_delay = 5  # 初始重试延迟（秒）
                max_retry_delay = 90  # 增加最大延迟以应对复杂任务
                logger.info(f"[重试优化] 默认版模式：重试次数{max_retries}次，增强超时处理能力")

            # 智能动态超时设置 - 根据分析类型和文本长度调整
            text_length = len(text) if text else 0

            # 基础超时时间
            base_connect_timeout = 90
            base_read_timeout = 320

            # 根据文本长度和分析类型智能调整超时时间
            if analysis_type in ["chapter_framework", "chapter_generation"]:
                # 章节生成框架需要更长时间
                connect_timeout = base_connect_timeout + 60  # 额外增加60秒
                read_timeout = base_read_timeout + 180       # 额外增加3分钟
                max_read_timeout = 1200  # 20分钟最大超时
                logger.info(f"[智能超时] 章节生成任务：连接超时{connect_timeout}秒，读取超时{read_timeout}秒")
            elif text_length > 50000:
                # 长文本需要更多时间
                timeout_multiplier = min(2.0, 1.0 + (text_length - 50000) / 100000)
                connect_timeout = int(base_connect_timeout * timeout_multiplier)
                read_timeout = int(base_read_timeout * timeout_multiplier)
                max_read_timeout = 1500  # 25分钟最大超时
                logger.info(f"[智能超时] 长文本({text_length}字符)：超时倍数{timeout_multiplier:.1f}，读取超时{read_timeout}秒")
            else:
                # 标准超时时间
                connect_timeout = base_connect_timeout
                read_timeout = base_read_timeout
                max_read_timeout = 900   # 15分钟最大超时

            # 添加到控制台日志
            try:
                novel_id = getattr(self, 'current_novel_id', None)
                if novel_id:
                    # 使用日志记录而不是直接调用add_analysis_log
                    logger.info(f"[API请求] 开始发送请求，最大重试次数: {max_retries}")
            except Exception as log_error:
                logger.error(f"添加API重试日志时出错: {str(log_error)}")

            for retry in range(max_retries):
                try:
                    logger.info(f"[API请求] 尝试 {retry+1}/{max_retries}")

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API请求] 尝试 {retry+1}/{max_retries}")
                    except Exception as log_error:
                        logger.error(f"添加API尝试日志时出错: {str(log_error)}")

                    # 根据重试次数动态调整超时时间，增加基础超时时间
                    current_connect_timeout = connect_timeout + retry * 30  # 每次重试增加30秒连接超时
                    current_read_timeout = read_timeout + retry * 90        # 每次重试增加90秒读取超时

                    # 精简版模式下，适度优化超时设置，但不过度减少以避免超时
                    if prompt_template == "simplified":
                        # 精简版适度减少超时时间，但保持足够的处理时间
                        current_connect_timeout = max(60, int(current_connect_timeout * 0.9))   # 连接超时减少10%
                        current_read_timeout = max(280, int(current_read_timeout * 0.9))        # 读取超时减少10%
                        logger.info(f"[超时优化] 精简版模式：适度优化超时设置，连接超时{current_connect_timeout}秒，读取超时{current_read_timeout}秒（保证稳定性）")

                    # 限制最大超时时间，增加上限以应对复杂任务
                    current_connect_timeout = min(current_connect_timeout, 240)  # 最大连接超时4分钟
                    current_read_timeout = min(current_read_timeout, max_read_timeout)  # 使用配置的最大读取超时

                    logger.info(f"[API请求] 设置连接超时{current_connect_timeout}秒，读取超时{current_read_timeout}秒")

                    # 根据stream参数决定是否使用流式输出
                    if stream:
                        # 流式输出模式 - 特别适用于整本书分析
                        logger.info(f"[流式输出] 启用流式输出模式，适用于大量内容分析")

                        # 为流式输出调整请求体
                        if self.model == "deepseek-r1":
                            # DeepSeek R1 支持流式输出
                            payload["parameters"] = {"stream": True, "incremental_output": True}
                            # 使用DashScope API格式
                            stream_endpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
                            stream_headers = headers.copy()
                            stream_headers["X-DashScope-SSE"] = "enable"

                            # 转换为DashScope格式
                            stream_payload = {
                                "model": "deepseek-r1",
                                "input": {
                                    "messages": [
                                        {
                                            "role": "user",
                                            "content": prompt
                                        }
                                    ]
                                },
                                "parameters": {
                                    "result_format": "message",
                                    "incremental_output": True,
                                    "max_tokens": max_tokens,
                                    "temperature": payload.get("input", {}).get("parameters", {}).get("temperature", 0.1)
                                }
                            }

                            logger.info(f"[流式输出] 使用DeepSeek R1流式API: {stream_endpoint}")
                            response = requests.post(stream_endpoint, headers=stream_headers, json=stream_payload,
                                                   timeout=(current_connect_timeout, current_read_timeout), stream=True)
                        else:
                            # 其他模型的流式输出支持
                            payload["parameters"]["stream"] = True
                            payload["parameters"]["incremental_output"] = True
                            response = requests.post(clean_endpoint, headers=headers, json=payload,
                                                   timeout=(current_connect_timeout, current_read_timeout), stream=True)
                    else:
                        # 标准非流式输出
                        response = requests.post(clean_endpoint, headers=headers, json=payload,
                                               timeout=(current_connect_timeout, current_read_timeout))

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API请求] 请求已发送，状态码: {response.status_code}")
                    except Exception as log_error:
                        logger.error(f"添加API响应状态日志时出错: {str(log_error)}")

                    break
                except Exception as e:
                    # 处理所有可能的异常，包括连接错误、超时、SSL错误等
                    error_type = type(e).__name__
                    error_msg = str(e)

                    if retry < max_retries - 1:
                        # 计算当前重试的延迟时间（指数退避策略）
                        current_retry_delay = min(base_retry_delay * (2 ** retry), max_retry_delay)
                        logger.warning(f"[API请求] 尝试 {retry+1} 失败: {error_type}: {error_msg}，将在 {current_retry_delay} 秒后重试")

                        # 添加到控制台日志
                        try:
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                # 使用日志记录而不是直接调用add_analysis_log
                                logger.warning(f"[API请求] 尝试 {retry+1} 失败: {error_type}: {error_msg[:100]}，将在 {current_retry_delay} 秒后重试")
                        except Exception as log_error:
                            logger.error(f"添加API失败日志时出错: {str(log_error)}")

                        # 根据错误类型调整重试策略，增强超时处理
                        if isinstance(e, requests.exceptions.Timeout):
                            logger.warning(f"[API请求] 检测到超时错误，当前读取超时: {current_read_timeout}秒")
                            logger.warning(f"[API请求] 尝试 {retry+1} 失败: 请求超时，将在 {current_retry_delay} 秒后重试")

                            # 友好的超时提示
                            if prompt_template == "simplified":
                                logger.info(f"[超时处理] 精简版模式：建议进一步优化文本长度或使用更小的max_tokens")
                            else:
                                logger.info(f"[超时处理] 默认版模式：正在增加下次请求的超时时间")

                            # 下次请求增加超时时间
                            headers["X-Timeout-Retry"] = "true"
                            # 动态增加下次超时时间，但不超过最大值
                            read_timeout = min(read_timeout + 120, max_read_timeout)  # 每次增加120秒
                            connect_timeout = min(connect_timeout + 30, 240)  # 每次增加30秒
                            logger.info(f"[超时处理] 下次重试将使用更长超时: 连接{connect_timeout}秒, 读取{read_timeout}秒")

                        elif isinstance(e, requests.exceptions.ConnectionError):
                            logger.warning(f"[API请求] 连接错误，可能是网络问题: {error_msg}")
                            logger.info(f"[连接处理] 建议检查网络连接或API服务状态")

                        elif isinstance(e, requests.exceptions.RequestException):
                            logger.warning(f"[API请求] 请求错误: {error_msg}")
                            logger.info(f"[请求处理] 建议检查请求参数或API配置")

                        # 计算当前重试的延迟时间（指数退避策略）
                        current_retry_delay = min(base_retry_delay * (2 ** retry), max_retry_delay)
                        logger.warning(f"[API请求] 将在 {current_retry_delay} 秒后重试")

                        # 添加到控制台日志
                        try:
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                # 使用日志记录而不是直接调用add_analysis_log
                                logger.warning(f"[API请求] 将在 {current_retry_delay} 秒后重试 (尝试 {retry+2}/{max_retries})")
                        except Exception as log_error:
                            logger.error(f"添加API重试日志时出错: {str(log_error)}")

                        time.sleep(current_retry_delay)
                    else:
                        # 计算总请求时间
                        total_request_time = time.time() - request_start_time

                        logger.error(f"[API请求] 请求 ID: {request_id} 失败")
                        logger.error(f"[API请求] 所有重试都失败: {error_type}: {error_msg}")
                        logger.error(f"[API请求] 总请求时间: {total_request_time:.2f}秒")

                        # 添加到控制台日志
                        try:
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                # 使用日志记录而不是直接调用add_analysis_log
                                logger.error(f"[API请求] 所有重试都失败: {error_type}: {error_msg[:100]}")
                                logger.error(f"[API请求] 请尝试检查网络连接或API密钥是否正确")
                        except Exception as log_error:
                            logger.error(f"添加API失败日志时出错: {str(log_error)}")

                        # 记录API调用失败
                        api_log.error_type = error_type
                        api_log.error_message = error_msg
                        self._save_api_log(api_log)

                        # 返回错误信息而不是抛出异常，让系统能够继续运行
                        return {
                            "error": f"API调用失败: {error_type}: {error_msg}",
                            "type": analysis_type
                        }

            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            total_request_time = end_time - request_start_time  # 总请求时间（包括重试）

            logger.info(f"[API响应] 请求 ID: {request_id} 完成")
            logger.info(f"[API响应] 请求耗时: {end_time - start_time:.2f}秒")
            logger.info(f"[API响应] 总请求时间（含重试）: {total_request_time:.2f}秒")
            logger.info(f"[API响应] 状态码: {response.status_code}")

            # 更新API日志
            api_log.status_code = response.status_code
            api_log.response_time = response_time
            api_log.response_size = len(response.content) if response.content else 0

            if response.headers:
                logger.info(f"[API响应] 响应头: {dict(response.headers)}")

            # 尝试获取响应内容
            try:
                response_text = response.text
                logger.info(f"[API响应] 响应内容（前500字符）: {response_text[:500]}...")
            except Exception as e:
                logger.warning(f"[API响应] 无法获取响应文本: {str(e)}")
                api_log.error_type = "response_parse_error"
                api_log.error_message = str(e)

            if response.status_code == 200:
                try:
                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API响应] 收到成功响应 (状态码: 200)，正在解析...")
                    except Exception as log_error:
                        logger.error(f"添加API响应日志时出错: {str(log_error)}")

                    # 处理流式输出响应
                    if stream:
                        logger.info(f"[流式输出] 开始处理流式响应")
                        return self._handle_stream_response(response, analysis_type, progress_callback)

                    # 处理标准响应
                    response_json = response.json()

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API响应] 成功解析JSON响应")
                    except Exception as log_error:
                        logger.error(f"添加API响应解析日志时出错: {str(log_error)}")

                    # 提取令牌使用信息 - 根据不同模型处理不同的响应格式
                    if self.model == "deepseek-r1":
                        # DeepSeek R1 模型的响应格式
                        output = response_json.get("output", {})
                        usage = output.get("usage", {})
                        prompt_tokens = usage.get("prompt_tokens", 0)
                        completion_tokens = usage.get("completion_tokens", 0)
                        total_tokens = prompt_tokens + completion_tokens
                    elif self.model == "deepseek-r1-0528-official":
                        # DeepSeek R1-0528 官方模型的响应格式
                        usage = response_json.get("usage", {})
                        prompt_tokens = usage.get("prompt_tokens", 0)
                        completion_tokens = usage.get("completion_tokens", 0)
                        total_tokens = usage.get("total_tokens", prompt_tokens + completion_tokens)
                    else:
                        # 通义千问等其他模型的响应格式
                        usage = response_json.get("usage", {})
                        prompt_tokens = usage.get("input_tokens", 0)
                        completion_tokens = usage.get("output_tokens", 0)
                        total_tokens = usage.get("total_tokens", 0)

                    logger.info(f"[API响应] 令牌使用信息: 输入={prompt_tokens}, 输出={completion_tokens}, 总计={total_tokens}")

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API响应] 令牌使用: 输入={prompt_tokens}, 输出={completion_tokens}, 总计={total_tokens}")
                    except Exception as log_error:
                        logger.error(f"添加API令牌日志时出错: {str(log_error)}")

                    # 记录响应结构，帮助调试
                    logger.info(f"[API响应] 响应结构: {list(response_json.keys())}")

                    # 提取和处理内容 - 适配阿里云DeepSeek API的响应格式
                    # 尝试多种可能的响应格式
                    content = None
                    reasoning_content = ""  # 🔧 修复：确保reasoning_content在所有情况下都被初始化

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API响应] 正在提取响应内容...")
                    except Exception as log_error:
                        logger.error(f"添加API内容提取日志时出错: {str(log_error)}")

                    # 记录响应JSON，帮助调试
                    logger.info(f"[API响应] 响应JSON摘要: {json.dumps(response_json)[:500]}...")

                    # 根据不同模型处理不同的响应格式
                    if self.model == "deepseek-r1":
                        # DeepSeek R1 模型的响应格式
                        output = response_json.get("output", {})
                        if output:
                            logger.info(f"[API响应] 找到output字段: {output}")

                            # 添加到控制台日志
                            try:
                                novel_id = getattr(self, 'current_novel_id', None)
                                if novel_id:
                                    # 使用日志记录而不是直接调用add_analysis_log
                                    logger.info(f"[API响应] 找到output字段，继续解析...")
                            except Exception as log_error:
                                logger.error(f"添加API输出字段日志时出错: {str(log_error)}")

                            # DeepSeek R1 模型可能在output.text中返回内容
                            content = output.get("text", "")

                            # 如果没有找到text字段，尝试从choices中获取
                            if not content and "choices" in output and len(output["choices"]) > 0:
                                choice = output["choices"][0]
                                logger.info(f"[API响应] 找到choice: {choice}")

                                # 尝试从message.content中获取内容
                                if "message" in choice and "content" in choice["message"]:
                                    content = choice["message"]["content"]
                                    logger.info(f"[API响应] 从choice.message.content中获取到内容")
                            if content:
                                logger.info(f"[API响应] 从DeepSeek R1 output.text字段获取到内容: {content[:200]}...")

                                # 添加到控制台日志
                                try:
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        # 使用日志记录而不是直接调用add_analysis_log
                                        logger.info(f"[API响应] 成功提取内容，长度: {len(content)} 字符")
                                except Exception as log_error:
                                    logger.error(f"添加API内容提取日志时出错: {str(log_error)}")
                    elif self.model == "deepseek-r1-0528-official":
                        # DeepSeek R1-0528 官方模型的响应格式
                        logger.info(f"[API响应] 处理DeepSeek R1-0528官方模型响应")

                        # 官方模型的响应格式: choices[0].message.content/reasoning_content
                        choices = response_json.get("choices", [])
                        if choices and len(choices) > 0:
                            choice = choices[0]
                            logger.info(f"[API响应] 找到choice: {choice}")

                            message = choice.get("message", {})
                            if message:
                                content = message.get("content", "")
                                reasoning_content = message.get("reasoning_content", "")

                                if content:
                                    logger.info(f"[API响应] 从官方模型choices[0].message.content获取到内容: {content[:200]}...")
                                if reasoning_content:
                                    logger.info(f"[API响应] 从官方模型choices[0].message.reasoning_content获取到推理内容: {len(reasoning_content)} 字符")
                                else:
                                    # 如果没有直接的reasoning_content，但有reasoning_tokens，说明推理内容可能在其他地方
                                    reasoning_tokens = usage.get('completion_tokens_details', {}).get('reasoning_tokens', 0)
                                    if reasoning_tokens > 0:
                                        logger.info(f"[API响应] 检测到推理令牌: {reasoning_tokens}，但未找到reasoning_content字段")
                                        # 生成推理内容占位符
                                        reasoning_content = f"推理过程已生成（{reasoning_tokens}个推理令牌），但API未返回详细推理内容。"
                                        logger.info(f"[API响应] 生成推理内容占位符，长度: {len(reasoning_content)}")

                                # 添加到控制台日志
                                try:
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        logger.info(f"[API响应] 成功提取官方模型内容，长度: {len(content)} 字符")
                                        if reasoning_content:
                                            logger.info(f"[API响应] 成功提取推理内容，长度: {len(reasoning_content)} 字符")
                                except Exception as log_error:
                                    logger.error(f"添加API内容提取日志时出错: {str(log_error)}")

                        # 如果没有找到内容，尝试其他可能的位置
                        if not content:
                            content = response_json.get("content", "")
                            if content:
                                logger.info(f"[API响应] 从顶层content字段获取到内容: {content[:200]}...")
                    elif self.model == "qwen-qvq-max":
                        # 通义千问-QVQ-Max 模型的响应格式
                        logger.info(f"[API响应] 处理通义千问-QVQ-Max模型响应")

                        # 尝试多种可能的响应格式
                        # 1. 标准格式: output -> choices -> message -> content
                        output = response_json.get("output", {})
                        if output:
                            logger.info(f"[API响应] 找到output字段: {output}")

                            choices = output.get("choices", [])
                            if choices and len(choices) > 0:
                                choice = choices[0]
                                logger.info(f"[API响应] 找到choice: {choice}")

                                message = choice.get("message", {})
                                logger.info(f"[API响应] 找到message: {message}")

                                content = message.get("content", "")
                                if content:
                                    logger.info(f"[API响应] 从标准路径获取到内容: {content[:200]}...")

                            # 2. 如果没有找到content，尝试直接从output中获取text
                            if not content:
                                content = output.get("text", "")
                                if content:
                                    logger.info(f"[API响应] 从output.text获取到内容: {content[:200]}...")

                        # 3. 如果还没有找到内容，尝试从response_json的顶层获取
                        if not content:
                            content = response_json.get("text", "")
                            if content:
                                logger.info(f"[API响应] 从顶层text字段获取到内容: {content[:200]}...")

                        # 4. 如果还没有找到内容，尝试从response_json的其他可能位置获取
                        if not content:
                            content = response_json.get("content", "")
                            if content:
                                logger.info(f"[API响应] 从顶层content字段获取到内容: {content[:200]}...")

                        # 5. 如果所有尝试都失败，记录完整的响应用于调试
                        if not content:
                            logger.warning(f"[API响应] 无法从标准位置提取内容，完整响应: {response_json}")
                            # 使用响应文本作为内容，以避免返回空内容
                            content = str(response_json)
                            logger.warning(f"[API响应] 使用完整响应作为内容: {content[:200]}...")

                        # 添加到控制台日志
                        try:
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                logger.info(f"[API响应] 成功提取内容，长度: {len(content)} 字符")
                        except Exception as log_error:
                            logger.error(f"添加API内容提取日志时出错: {str(log_error)}")
                    else:
                        # 通义千问等其他模型的响应格式 - 标准格式 output -> choices -> message -> content
                        output = response_json.get("output", {})
                        if output:
                            logger.info(f"[API响应] 找到output字段: {output}")

                            # 添加到控制台日志
                            try:
                                novel_id = getattr(self, 'current_novel_id', None)
                                if novel_id:
                                    # 使用日志记录而不是直接调用add_analysis_log
                                    logger.info(f"[API响应] 找到output字段，继续解析...")
                            except Exception as log_error:
                                logger.error(f"添加API输出字段日志时出错: {str(log_error)}")

                            choices = output.get("choices", [])
                            if choices and len(choices) > 0:
                                choice = choices[0]
                                logger.info(f"[API响应] 找到choice: {choice}")

                                # 添加到控制台日志
                                try:
                                    from src.web.app import add_analysis_log
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        add_analysis_log(novel_id, f"[API响应] 找到choices字段，继续解析...", "info", analysis_type)
                                except Exception as log_error:
                                    logger.error(f"添加API选择字段日志时出错: {str(log_error)}")

                                message = choice.get("message", {})
                                logger.info(f"[API响应] 找到message: {message}")

                                # 添加到控制台日志
                                try:
                                    from src.web.app import add_analysis_log
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        add_analysis_log(novel_id, f"[API响应] 找到message字段，提取内容...", "info", analysis_type)
                                except Exception as log_error:
                                    logger.error(f"添加API消息字段日志时出错: {str(log_error)}")

                                content = message.get("content", "")
                                logger.info(f"[API响应] 从标准路径获取到内容: {content[:200]}...")

                                # 添加到控制台日志
                                try:
                                    from src.web.app import add_analysis_log
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        add_analysis_log(novel_id, f"[API响应] 成功提取内容，长度: {len(content)} 字符", "info", analysis_type)
                                except Exception as log_error:
                                    logger.error(f"添加API内容提取日志时出错: {str(log_error)}")

                    # 备用方式: 直接在响应中的content字段
                    if not content:
                        content = response_json.get("content", "")
                        if content:
                            logger.info(f"[API响应] 从顶层content字段获取到内容: {content[:200]}...")

                    # 方式3: 在data或result字段中
                    if not content:
                        data = response_json.get("data", {})
                        if data:
                            logger.info(f"[API响应] 找到data字段: {data}")
                            content = data.get("content", "")
                            if content:
                                logger.info(f"[API响应] 从data字段获取到内容: {content[:200]}...")

                    # 方式4: 在result字段中
                    if not content:
                        result = response_json.get("result", {})
                        if result:
                            logger.info(f"[API响应] 找到result字段: {result}")
                            content = result.get("content", "")
                            if content:
                                logger.info(f"[API响应] 从result字段获取到内容: {content[:200]}...")

                    # 方式5: 在response字段中
                    if not content:
                        response_field = response_json.get("response", "")
                        if response_field:
                            logger.info(f"[API响应] 找到response字段: {response_field}")
                            if isinstance(response_field, str):
                                content = response_field
                                logger.info(f"[API响应] 从response字段获取到内容: {content[:200]}...")

                    if content:
                        # 检查内容长度是否合理
                        content_length = len(content.strip())

                        # 对于写作类任务，内容长度应该更长 - 修复版本
                        writing_analysis_types = [
                            "chapter_content_generation", "chapter_framework", "chapter_generation",
                            "content_generation", "writing", "story_generation", "novel_generation"
                        ]

                        # 检查是否是明显的错误内容（如21字符的错误信息）
                        is_error_content = self._is_error_content(content)

                        if is_error_content:
                            logger.error(f"[API响应] 检测到错误内容：{content}")
                            # 记录API错误
                            api_log.error_type = "error_content_detected"
                            api_log.error_message = f"API返回错误内容：{content}"
                            api_log.response_data = content[:500]
                            self._save_api_log(api_log)

                            return {
                                "error": f"API返回错误内容：{content}",
                                "content": content,
                                "type": analysis_type
                            }

                        # 放宽写作任务的长度要求，特别是第一批次基础框架
                        min_length_required = 50  # 大幅降低默认最小长度
                        if analysis_type in writing_analysis_types:
                            # 写作任务的最小长度要求更宽松
                            if "framework" in analysis_type or "第一批次" in content:
                                min_length_required = 100  # 基础框架允许更短
                            else:
                                min_length_required = 200  # 其他写作任务适度要求

                        if content_length < min_length_required:
                            logger.warning(f"[API响应] 内容长度较短：{content_length}字符，低于{analysis_type}任务的建议长度{min_length_required}字符")
                            logger.warning(f"[API响应] 内容预览: {content[:200]}...")

                            # 检查内容质量而不是直接拒绝
                            if self._is_valid_writing_content(content, analysis_type):
                                logger.info(f"[API响应] 虽然长度较短，但内容有效，继续处理")
                            else:
                                logger.error(f"[API响应] 内容长度过短且质量不符合要求")
                                # 记录API错误
                                api_log.error_type = "content_too_short"
                                api_log.error_message = f"API返回内容过短且质量不符合要求：{content_length}字符"
                                api_log.response_data = content[:500]
                                self._save_api_log(api_log)

                                return {
                                    "error": f"API返回内容过短且质量不符合要求：{content_length}字符",
                                    "content": content,
                                    "type": analysis_type
                                }

                        logger.info(f"[API响应] 内容摘要: {content[:200]}...")
                        logger.info(f"[API处理] 成功解析 {analysis_type} 分析结果，内容长度: {content_length}字符")

                        # 添加到控制台日志
                        try:
                            from src.web.app import add_analysis_log
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                add_analysis_log(novel_id, f"[API响应] 成功获取 {analysis_type} 分析结果", "info", analysis_type)
                                add_analysis_log(novel_id, f"[API响应] 内容长度: {len(content)} 字符", "debug", analysis_type)
                        except Exception as log_error:
                            logger.error(f"添加API响应日志时出错: {str(log_error)}")

                        # 记录令牌使用量和费用
                        cost_info = self._record_api_call(prompt_tokens, completion_tokens)

                        # 更新API日志
                        try:
                            api_log.input_tokens = prompt_tokens
                            api_log.output_tokens = completion_tokens
                            api_log.total_tokens = total_tokens
                            api_log.novel_id = getattr(self, 'current_novel_id', None)
                            api_log.analysis_type = analysis_type

                            if cost_info:
                                api_log.input_cost = cost_info["input_cost"]
                                api_log.output_cost = cost_info["output_cost"]
                                api_log.total_cost = cost_info["total_cost"]

                            # 记录请求和响应数据（截断以避免数据库存储问题）
                            api_log.parameters = {
                                "model": self.model,
                                "analysis_type": analysis_type,
                                "max_tokens": max_tokens,
                                "text_length": len(text)
                            }
                        except Exception as e:
                            logger.error(f"更新API日志时出错: {str(e)}")

                            # 添加到控制台日志
                            try:
                                from src.web.app import add_analysis_log
                                novel_id = getattr(self, 'current_novel_id', None)
                                if novel_id:
                                    add_analysis_log(novel_id, f"[API费用] 本次调用: 输入={prompt_tokens}令牌, 输出={completion_tokens}令牌, 总计={total_tokens}令牌", "info", analysis_type)
                                    add_analysis_log(novel_id, f"[API费用] 本次费用: 输入={cost_info['input_cost']:.4f}元, 输出={cost_info['output_cost']:.4f}元, 总计={cost_info['total_cost']:.4f}元", "info", analysis_type)

                                    # 获取累计统计信息
                                    stats = self.get_api_call_stats()
                                    add_analysis_log(novel_id, f"[API统计] 累计调用: {stats['total_calls']}次, 累计令牌: {stats['total_tokens']}个, 累计费用: {stats['total_cost']:.4f}元", "info", analysis_type)
                            except Exception as log_error:
                                logger.error(f"添加API费用日志时出错: {str(log_error)}")

                        # 保存API日志
                        self._save_api_log(api_log)

                        # 检查内容是否是JSON格式的字符串，如果是，尝试提取实际内容
                        if isinstance(content, str) and (content.startswith('{"output":') or content.startswith('{"choices":')):
                            try:
                                # 尝试解析JSON
                                content_json = json.loads(content)
                                logger.info(f"[API处理] 检测到JSON格式的内容，尝试提取实际内容")

                                # 尝试从不同路径提取内容
                                extracted_content = None

                                # 路径1: output.choices[0].message.content
                                if 'output' in content_json and 'choices' in content_json['output'] and len(content_json['output']['choices']) > 0:
                                    choice = content_json['output']['choices'][0]
                                    if 'message' in choice and 'content' in choice['message']:
                                        extracted_content = choice['message']['content']
                                        logger.info(f"[API处理] 从output.choices[0].message.content提取到内容")

                                        # 尝试提取推理过程内容
                                        if 'reasoning_content' in choice['message']:
                                            reasoning_content = choice['message']['reasoning_content']
                                            logger.info(f"[API处理] 从output.choices[0].message.reasoning_content提取到推理过程内容，长度: {len(reasoning_content)} 字符")

                                # 路径2: output.text
                                if not extracted_content and 'output' in content_json and 'text' in content_json['output']:
                                    extracted_content = content_json['output']['text']
                                    logger.info(f"[API处理] 从output.text提取到内容")

                                # 如果成功提取到内容，更新content
                                if extracted_content:
                                    logger.info(f"[API处理] 成功从JSON中提取内容，长度: {len(extracted_content)} 字符")
                                    content = extracted_content

                                    # 添加到控制台日志
                                    try:
                                        from src.web.app import add_analysis_log
                                        novel_id = getattr(self, 'current_novel_id', None)
                                        if novel_id:
                                            add_analysis_log(novel_id, f"[API处理] 成功从JSON中提取内容，长度: {len(extracted_content)} 字符", "info", analysis_type)
                                    except Exception as log_error:
                                        logger.error(f"添加API内容提取日志时出错: {str(log_error)}")
                            except json.JSONDecodeError as e:
                                logger.error(f"[API处理] 解析JSON内容时出错: {str(e)}")
                                # 保持原始内容不变

                        # 检查是否是推理过程分析
                        if analysis_type.endswith("_reasoning"):
                            logger.info(f"这是推理过程分析，将content同时作为reasoning_content返回")
                            result = {
                                "type": analysis_type,
                                "content": content,
                                "reasoning_content": content
                            }
                        else:
                            # 尝试从API响应中提取reasoning_content
                            if not reasoning_content:  # 如果之前没有提取到推理内容
                                try:
                                    # 对于DeepSeek官方API，从choices[0].message.reasoning_content提取
                                    if self.model == "deepseek-r1-0528-official" and 'choices' in response_json and len(response_json['choices']) > 0:
                                        choice = response_json['choices'][0]
                                        message = choice.get('message', {})
                                        if 'reasoning_content' in message:
                                            reasoning_content = message['reasoning_content']
                                            logger.info(f"从DeepSeek官方API响应中提取到reasoning_content，长度: {len(reasoning_content)} 字符")

                                    # 对于其他模型，从output.choices[0].message.reasoning_content提取
                                    elif 'output' in response_json and 'choices' in response_json['output'] and len(response_json['output']['choices']) > 0:
                                        choice = response_json['output']['choices'][0]
                                        if 'message' in choice and 'reasoning_content' in choice['message']:
                                            reasoning_content = choice['message']['reasoning_content']
                                            logger.info(f"从API响应中提取到reasoning_content，长度: {len(reasoning_content)} 字符")
                                        # 如果没有reasoning_content字段，但有content字段，则使用content字段作为推理过程内容
                                        elif 'message' in choice and 'content' in choice['message']:
                                            # 对于某些特定的分析类型，将content作为reasoning_content
                                            if analysis_type in ["language_style", "rhythm_pacing", "structure", "character_relationships"]:
                                                reasoning_content = choice['message']['content']
                                                logger.info(f"使用content字段作为reasoning_content，长度: {len(reasoning_content)} 字符")
                                except Exception as e:
                                    logger.error(f"提取reasoning_content时出错: {str(e)}")

                            # 对于普通分析，添加reasoning_content字段
                            result = {
                                "type": analysis_type,
                                "content": content,
                                "reasoning_content": reasoning_content
                            }

                        logger.info(f"返回分析结果: type={result['type']}, content长度={len(result['content'])}, reasoning_content长度={len(result['reasoning_content'])}")
                        return result
                    else:
                        # 详细记录响应结构以便调试
                        logger.error(f"API响应中没有找到内容")
                        logger.error(f"响应状态码: {response.status_code}")
                        logger.error(f"响应头: {dict(response.headers) if response.headers else 'None'}")
                        logger.error(f"完整响应JSON: {json.dumps(response_json, ensure_ascii=False, indent=2)}")

                        # 检查是否是空响应或错误响应
                        if not response_json or response_json == {}:
                            error_msg = "API返回空响应"
                        elif "error" in response_json:
                            error_msg = f"API返回错误: {response_json.get('error', '未知错误')}"
                        elif "message" in response_json:
                            error_msg = f"API返回消息: {response_json.get('message', '未知消息')}"
                        else:
                            error_msg = f"API响应格式异常，无法提取内容。响应结构: {list(response_json.keys())}"

                        logger.error(error_msg)

                        # 记录API错误
                        api_log.error_type = "content_not_found"
                        api_log.error_message = error_msg
                        api_log.response_data = json.dumps(response_json)[:1000]  # 保存响应数据用于调试
                        self._save_api_log(api_log)
                        return {"error": error_msg}
                except json.JSONDecodeError as e:
                    error_msg = f"API响应不是有效的JSON: {str(e)}, 响应内容: {response.text[:500]}..."
                    logger.error(error_msg)
                    # 记录API错误
                    api_log.error_type = "json_decode_error"
                    api_log.error_message = error_msg
                    self._save_api_log(api_log)
                    return {"error": error_msg}
            elif response.status_code == 401:
                error_msg = f"API授权失败：请检查API密钥是否正确。状态码={response.status_code}, 响应={response.text}"
                logger.error(error_msg)
                # 记录API错误
                api_log.error_type = "authentication_error"
                api_log.error_message = error_msg
                self._save_api_log(api_log)
                return {"error": error_msg}
            elif response.status_code == 400:
                error_msg = f"API请求参数错误：请检查请求参数。状态码={response.status_code}, 响应={response.text}"
                logger.error(error_msg)
                # 记录API错误
                api_log.error_type = "bad_request"
                api_log.error_message = error_msg
                self._save_api_log(api_log)
                return {"error": error_msg}
            elif response.status_code == 500:
                # 处理500错误，特别是超时错误，提供友好的错误处理
                try:
                    response_json = response.json()
                    if response_json.get("code") == "RequestTimeOut":
                        error_msg = f"API请求超时：{response_json.get('message', '请求超时，请稍后重试')}"
                        logger.error(error_msg)

                        # 提供友好的超时处理建议
                        if prompt_template == "simplified":
                            logger.info(f"[超时处理建议] 精简版模式：")
                            logger.info(f"  1. 当前已启用降本增效优化，但仍出现超时")
                            logger.info(f"  2. 建议进一步减少文本长度或使用更小的max_tokens")
                            logger.info(f"  3. 可以尝试将长文本分段处理")
                            friendly_error = "精简版模式下仍出现超时，建议进一步优化文本长度或分段处理"
                        else:
                            logger.info(f"[超时处理建议] 默认版模式：")
                            logger.info(f"  1. 建议切换到精简版模式以减少处理时间")
                            logger.info(f"  2. 或者将长文本分段处理")
                            logger.info(f"  3. 系统将在下次重试时自动增加超时时间")
                            friendly_error = "请求超时，建议切换到精简版模式或分段处理文本"

                        # 记录API错误
                        api_log.error_type = "timeout_error"
                        api_log.error_message = error_msg
                        self._save_api_log(api_log)
                        return {"error": friendly_error, "original_error": error_msg, "suggestions": "timeout"}
                    else:
                        error_msg = f"API服务器内部错误: 状态码={response.status_code}, 响应={response.text}"
                        logger.error(error_msg)
                        logger.info(f"[服务器错误处理] 建议稍后重试或联系技术支持")
                        api_log.error_type = "server_error"
                        api_log.error_message = error_msg
                        self._save_api_log(api_log)
                        return {"error": "API服务器暂时不可用，请稍后重试", "original_error": error_msg}
                except json.JSONDecodeError:
                    error_msg = f"API服务器内部错误: 状态码={response.status_code}, 响应={response.text}"
                    logger.error(error_msg)
                    logger.info(f"[响应解析错误] API返回了无效的JSON格式")
                    api_log.error_type = "server_error"
                    api_log.error_message = error_msg
                    self._save_api_log(api_log)
                    return {"error": "API服务器返回无效响应，请稍后重试", "original_error": error_msg}
            else:
                error_msg = f"API调用失败: 状态码={response.status_code}, 响应={response.text}"
                logger.error(error_msg)
                # 记录API错误
                api_log.error_type = "api_error"
                api_log.error_message = error_msg
                self._save_api_log(api_log)
                return {"error": error_msg}

        except requests.exceptions.Timeout:
            error_msg = "API请求超时，请稍后重试"
            logger.error(error_msg)
            return {"error": error_msg}
        except requests.exceptions.ConnectionError:
            error_msg = "连接API服务器失败，请检查网络连接"
            logger.error(error_msg)
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"API调用过程中出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"error": error_msg}

    def _is_error_content(self, content: str) -> bool:
        """
        检查内容是否是错误信息

        Args:
            content: 要检查的内容

        Returns:
            True如果是错误内容，False如果是正常内容
        """
        if not content:
            return True

        content_stripped = content.strip()
        content_length = len(content_stripped)

        # 首先检查明确的错误信息模式
        error_patterns = [
            "API返回的数据格式不正确",
            "生成内容失败",
            "API调用失败",
            "请求超时",
            "服务器错误",
            "内部错误",
            "网络错误",
            "连接失败",
            "参数错误",
            "授权失败",
            "格式错误",
            "解析失败",
            "无效响应",
            "error",
            "Error",
            "ERROR",
            "failed",
            "Failed",
            "FAILED"
        ]

        content_lower = content_stripped.lower()
        for pattern in error_patterns:
            if pattern.lower() in content_lower:
                return True

        # 检查是否包含写作特征
        writing_indicators = [
            r'第\d+章',  # 章节标题
            r'#\s*第\d+章',  # Markdown章节标题
            r'[。！？]',  # 中文标点
            r'[.!?]',  # 英文标点
            r'"[^"]*"',  # 对话
            r'"[^"]*"',  # 中文引号对话
            r'[他她它][们]?[走跑看听想]',  # 人物动作
        ]

        has_writing_features = any(re.search(indicator, content_stripped) for indicator in writing_indicators)

        # 如果内容很短但包含写作特征，不认为是错误
        if content_length <= 30 and has_writing_features:
            return False

        # 如果内容很短且没有写作特征，认为是错误
        if content_length <= 15:
            return True

        return False

    def _is_valid_writing_content(self, content: str, analysis_type: str) -> bool:
        """
        检查内容是否是有效的写作内容

        Args:
            content: 要检查的内容
            analysis_type: 分析类型

        Returns:
            True如果是有效的写作内容，False如果不是
        """
        if not content or len(content.strip()) < 20:
            return False

        # 检查写作内容的特征
        writing_indicators = [
            r'第\d+章',  # 章节标题
            r'#\s*第\d+章',  # Markdown章节标题
            r'[。！？]',  # 中文标点
            r'[.!?]',  # 英文标点
            r'"[^"]*"',  # 对话
            r'"[^"]*"',  # 中文引号对话
            r'[他她它][们]?[走跑看听想]',  # 人物动作
            r'[突然忽然马上立刻]',  # 时间副词
            r'[这那][个些]',  # 指示词
            r'说道?[:：]',  # 说话动作
            r'[A-Za-z\u4e00-\u9fa5]{5,}',  # 连续的文字内容
        ]

        # 检查是否包含写作特征
        matches = 0
        for indicator in writing_indicators:
            if re.search(indicator, content):
                matches += 1

        # 如果包含多个写作特征，认为是有效内容
        if matches >= 2:
            return True

        # 对于基础框架，要求更宽松
        if "framework" in analysis_type:
            return matches >= 1

        return False

    def _compress_prompt_intelligently(self, prompt: str, analysis_type: str, max_tokens: int = None, prompt_template: str = None) -> str:
        """
        智能压缩提示词以减少输入token（新降本增效方案2）

        Args:
            prompt: 原始提示词
            analysis_type: 分析类型
            max_tokens: 最大token数量
            prompt_template: 提示词模板

        Returns:
            压缩后的提示词
        """
        try:
            # 移除多余的空行和空格
            compressed = re.sub(r'\n{3,}', '\n\n', prompt)  # 将3个以上的连续换行替换为2个
            compressed = re.sub(r' {2,}', ' ', compressed)   # 将2个以上的连续空格替换为1个

            # 移除示例中的冗余描述
            compressed = re.sub(r'例如：[^。]*。', '', compressed)  # 移除"例如："开头的句子
            compressed = re.sub(r'比如：[^。]*。', '', compressed)  # 移除"比如："开头的句子

            # 简化重复的指导语
            compressed = re.sub(r'请注意[^。]*。\s*请注意[^。]*。', '请注意相关要求。', compressed)
            compressed = re.sub(r'请确保[^。]*。\s*请确保[^。]*。', '请确保质量要求。', compressed)

            # 根据分析类型进行特定压缩
            if analysis_type in ["paragraph_length", "sentence_variation"]:
                # 对于格式类分析，可以移除部分文学性描述
                compressed = re.sub(r'文学性[^。]*。', '', compressed)
                compressed = re.sub(r'艺术性[^。]*。', '', compressed)

            # 压缩常见的冗余表达，并添加精简版特有的输出要求
            replacements = {
                '请进行详细的分析': '请分析',
                '请进行深入的分析': '请分析',
                '请进行全面的分析': '请分析',
                '请详细说明': '请说明',
                '请仔细分析': '请分析',
                '请认真分析': '请分析',
                '非常重要': '重要',
                '极其重要': '重要',
                '特别注意': '注意',
                '务必注意': '注意',
                # 精简版特有的输出要求
                '请提供详细的分析结果': '请提供简洁的分析要点',
                '请详细阐述': '请简要说明',
                '请全面分析': '请重点分析',
                '详细的推理过程': '简要推理',
                '深入的分析': '要点分析'
            }

            # 添加智能token优化指导
            try:
                from src.config.token_optimization_config import TokenOptimizationConfig

                # 获取当前使用的max_tokens（从之前的计算中获取）
                current_max_tokens = max_tokens if max_tokens else 4000

                # 获取质量优化提示词
                quality_prompt = TokenOptimizationConfig.get_quality_optimization_prompt(
                    dimension=analysis_type,
                    prompt_template=prompt_template or "default",
                    target_tokens=current_max_tokens
                )

                # 检查是否为写作相关功能
                writing_analysis_types = [
                    "chapter_content_generation", "chapter_framework", "chapter_generation",
                    "content_generation", "writing", "story_generation", "novel_generation"
                ]

                if analysis_type in writing_analysis_types:
                    # 写作功能不添加token限制，保持原有质量
                    logger.info(f"[写作功能保护] {analysis_type}为写作功能，不添加token优化限制")
                else:
                    # 分析功能添加智能优化指导
                    compressed += quality_prompt
                    logger.info(f"[Token优化] 已添加智能优化指导，目标tokens: {current_max_tokens}")

            except ImportError:
                # 降级到原有逻辑
                if '请按照以下格式输出' in compressed:
                    writing_analysis_types = [
                        "chapter_content_generation", "chapter_framework", "chapter_generation",
                        "content_generation", "writing", "story_generation", "novel_generation"
                    ]

                    if analysis_type in writing_analysis_types:
                        logger.info(f"[写作功能保护] 精简版模式：{analysis_type}为写作功能，不添加激进输出限制")
                    else:
                        compressed += '\n\n**精简版智能优化要求（仅限分析功能）：**\n- 表达方式：使用简洁明了的语言，避免冗余表达\n- 内容组织：结构清晰，重点突出\n- 分析深度：保持分析质量，充分利用已消耗的Token\n- 输出完整：不人为截断分析结果，确保分析价值最大化'

            for old, new in replacements.items():
                compressed = compressed.replace(old, new)

            # 应用新的优化策略
            compressed = self._optimize_prompt_structure(compressed, analysis_type)

            # 移除行首行尾的空白字符
            compressed = '\n'.join(line.strip() for line in compressed.split('\n') if line.strip())

            return compressed

        except Exception as e:
            logger.error(f"智能压缩提示词时出错: {str(e)}")
            return prompt  # 出错时返回原始提示词

    def _compress_text_intelligently(self, text: str, analysis_type: str) -> str:
        """
        智能压缩文本以减少输入token，同时保证分析质量（新降本增效方案3）

        Args:
            text: 原始文本
            analysis_type: 分析类型

        Returns:
            压缩后的文本
        """
        try:
            # 基础清理：移除多余的空行和空格
            compressed = re.sub(r'\n{3,}', '\n\n', text)  # 将3个以上的连续换行替换为2个
            compressed = re.sub(r' {2,}', ' ', compressed)   # 将2个以上的连续空格替换为1个

            # 移除一些常见的无意义标点重复
            compressed = re.sub(r'[.]{3,}', '...', compressed)  # 将3个以上的连续句点替换为3个
            compressed = re.sub(r'[!]{2,}', '!', compressed)    # 将2个以上的连续感叹号替换为1个
            compressed = re.sub(r'[?]{2,}', '?', compressed)    # 将2个以上的连续问号替换为1个

            # 移除多余的空白字符
            compressed = re.sub(r'[ \t]+', ' ', compressed)     # 将多个空格或制表符替换为单个空格
            compressed = re.sub(r'\n[ \t]+', '\n', compressed)  # 移除行首的空格和制表符

            # 根据分析类型进行智能压缩，保证质量
            if analysis_type in ["language_style", "rhythm_pacing", "structure"]:
                # 核心维度：保留更多细节，只做基础清理
                pass  # 已经完成基础清理
            elif analysis_type in ["paragraph_length", "sentence_variation"]:
                # 格式类分析：可以适度压缩重复描述
                compressed = re.sub(r'([，。！？])\1+', r'\1', compressed)  # 移除重复的标点
            else:
                # 其他维度：适度压缩，但保留关键内容
                # 移除过多的形容词重复
                compressed = re.sub(r'(很|非常|特别|极其|十分){2,}', r'\1', compressed)

            # 智能段落合并：将过短的段落合并，减少token但保持逻辑
            lines = compressed.split('\n')
            merged_lines = []
            current_paragraph = ""

            for line in lines:
                line = line.strip()
                if not line:
                    if current_paragraph:
                        merged_lines.append(current_paragraph)
                        current_paragraph = ""
                    merged_lines.append("")
                elif len(line) < 20 and current_paragraph:
                    # 短行合并到当前段落
                    current_paragraph += line
                else:
                    if current_paragraph:
                        merged_lines.append(current_paragraph)
                    current_paragraph = line

            if current_paragraph:
                merged_lines.append(current_paragraph)

            compressed = '\n'.join(merged_lines)

            # 移除行首行尾的空白字符
            compressed = '\n'.join(line.strip() for line in compressed.split('\n') if line.strip() or not line.strip())

            return compressed

        except Exception as e:
            logger.error(f"智能压缩文本时出错: {str(e)}")
            return text  # 出错时返回原始文本

    def _split_chapter_framework(self, text: str) -> List[str]:
        """
        将章节生成框架分成多个部分处理，避免超时

        Args:
            text: 章节生成框架文本

        Returns:
            分段后的文本列表
        """
        try:
            sections = []

            # 按照主要部分分割
            main_sections = [
                "## 原文信息",
                "## 人物连贯性要求",
                "## 章节过渡要求",
                "## 原文风格分析摘要",
                "## 写作任务",
                "【最高优先级指令",
                "在写作过程中，请实时追踪字数"
            ]

            current_section = ""
            for line in text.split('\n'):
                # 检查是否是新的主要部分
                is_new_section = any(section in line for section in main_sections)

                if is_new_section and current_section:
                    # 保存当前部分
                    sections.append(current_section.strip())
                    current_section = line + '\n'
                else:
                    current_section += line + '\n'

            # 添加最后一个部分
            if current_section:
                sections.append(current_section.strip())

            # 如果分段后仍然太长，进一步分割
            final_sections = []
            for section in sections:
                if len(section) > 15000:
                    # 进一步分割长段
                    sub_sections = self._split_long_section(section)
                    final_sections.extend(sub_sections)
                else:
                    final_sections.append(section)

            logger.info(f"章节框架分段完成：原文{len(text)}字符，分成{len(final_sections)}个部分")
            return final_sections

        except Exception as e:
            logger.error(f"分割章节框架时出错: {str(e)}")
            return [text]  # 出错时返回原始文本

    def _split_long_section(self, section: str) -> List[str]:
        """
        进一步分割长段落

        Args:
            section: 长段落文本

        Returns:
            分割后的段落列表
        """
        try:
            # 按段落分割
            paragraphs = section.split('\n\n')

            sub_sections = []
            current_sub = ""

            for paragraph in paragraphs:
                if len(current_sub + paragraph) > 12000:
                    if current_sub:
                        sub_sections.append(current_sub.strip())
                        current_sub = paragraph + '\n\n'
                    else:
                        # 单个段落就超过限制，强制分割
                        sub_sections.append(paragraph[:12000])
                        current_sub = paragraph[12000:] + '\n\n'
                else:
                    current_sub += paragraph + '\n\n'

            if current_sub:
                sub_sections.append(current_sub.strip())

            return sub_sections

        except Exception as e:
            logger.error(f"分割长段落时出错: {str(e)}")
            return [section]

    def _compress_chapter_framework(self, text: str) -> str:
        """
        专门针对章节生成框架的智能压缩

        Args:
            text: 章节生成框架文本

        Returns:
            压缩后的文本
        """
        try:
            compressed = text

            # 移除重复的指导语
            compressed = re.sub(r'(必须|请|应该|需要)[^。]*。\s*\1[^。]*。', r'\1相关要求。', compressed)

            # 简化分析摘要部分
            if "## 原文风格分析摘要" in compressed:
                # 保留核心分析，移除过于详细的描述
                compressed = re.sub(r'详细分析：[^#]*(?=#|$)', '核心要点：[已简化]', compressed)

            # 简化写作步骤
            if "### 写作步骤：" in compressed:
                # 保留核心步骤，移除详细说明
                compressed = re.sub(r'(\d+\. [^：]*：)[^0-9]*(?=\d+\.|$)', r'\1[已简化] ', compressed)

            # 移除多余的示例
            compressed = re.sub(r'例如：[^。]*。\s*', '', compressed)
            compressed = re.sub(r'比如：[^。]*。\s*', '', compressed)

            # 合并相似的要求
            compressed = re.sub(r'- ([^-\n]*)\n- \1[^-\n]*', r'- \1', compressed)

            # 基础清理
            compressed = re.sub(r'\n{3,}', '\n\n', compressed)
            compressed = re.sub(r' {2,}', ' ', compressed)

            logger.info(f"章节框架压缩完成：从{len(text)}字符压缩到{len(compressed)}字符，压缩率{(1-len(compressed)/len(text))*100:.1f}%")
            return compressed

        except Exception as e:
            logger.error(f"压缩章节框架时出错: {str(e)}")
            return text

    def _check_similar_analysis_result(self, text: str, analysis_type: str):
        """
        检查是否有相似的分析结果可以复用（新降本增效方案5）
        只在精简版模式下启用
        """
        try:
            # 简单的相似度检查：基于文本长度和类型
            text_length = len(text)

            # 对于格式类分析，如果文本长度相近，可以复用结果
            if analysis_type in ["paragraph_length", "sentence_variation"]:
                # 这里可以实现更复杂的相似度检查逻辑
                # 暂时返回None，表示没有找到可复用的结果
                pass

            return None
        except Exception as e:
            logger.error(f"检查相似分析结果时出错: {str(e)}")
            return None

    def _adapt_similar_result(self, similar_result: dict, text: str, analysis_type: str) -> dict:
        """
        适配相似的分析结果到当前文本（新降本增效方案5）
        只在精简版模式下启用
        """
        try:
            # 基础适配：更新文本长度相关的信息
            adapted_result = similar_result.copy()
            adapted_result["content"] = f"基于相似文本分析结果适配：{similar_result.get('content', '')}"
            adapted_result["reasoning_content"] = f"[复用优化] 基于相似文本的分析结果进行适配。原分析：{similar_result.get('reasoning_content', '')}"

            return adapted_result
        except Exception as e:
            logger.error(f"适配相似分析结果时出错: {str(e)}")
            return similar_result

    def _generate_cache_key(self, text: str, analysis_type: str, prompt_template: str) -> str:
        """
        生成缓存键（新降本增效方案4）
        只在精简版模式下启用
        """
        try:
            import hashlib
            # 使用文本哈希、分析类型和模板生成唯一键
            text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()[:16]
            return f"{prompt_template}_{analysis_type}_{text_hash}"
        except Exception as e:
            logger.error(f"生成缓存键时出错: {str(e)}")
            return f"{prompt_template}_{analysis_type}_{len(text)}"

    def _get_cached_result(self, cache_key: str):
        """
        获取缓存的分析结果（新降本增效方案4）
        只在精简版模式下启用
        """
        try:
            # 这里可以实现基于文件或数据库的缓存
            # 暂时返回None，表示没有缓存
            return None
        except Exception as e:
            logger.error(f"获取缓存结果时出错: {str(e)}")
            return None

    def _save_cached_result(self, cache_key: str, result: dict):
        """
        保存分析结果到缓存（新降本增效方案4）
        只在精简版模式下启用
        """
        try:
            # 这里可以实现基于文件或数据库的缓存保存
            # 暂时不实现具体逻辑
            pass
        except Exception as e:
            logger.error(f"保存缓存结果时出错: {str(e)}")

    def _try_merged_dimension_analysis(self, text: str, analysis_type: str, prompt_template: str) -> dict:
        """
        尝试维度合并分析（新降本增效方案9）
        一次API调用分析多个相关维度，大幅减少API调用次数
        """
        try:
            # 定义可以合并分析的维度组
            dimension_groups = {
                "style_group": ["language_style", "rhythm_pacing", "tone_mood"],
                "structure_group": ["structure", "paragraph_length", "sentence_variation"],
                "narrative_group": ["perspective_shifts", "dialogue_narrative", "character_development"]
            }

            # 查找当前维度所属的组
            target_group = None
            for group_name, dimensions in dimension_groups.items():
                if analysis_type in dimensions:
                    target_group = group_name
                    break

            if target_group:
                # 构建合并分析的提示词
                merged_prompt = f"""请同时分析以下文本的多个维度：
1. {analysis_type}（主要维度）
2. 相关维度的简要分析

文本内容：
{text[:3000]}  # 限制文本长度以控制token

请按以下格式输出：
## {analysis_type}分析
[详细分析内容]

## 相关维度简要分析
[其他维度的简要分析]
"""

                # 模拟合并分析结果（实际应该调用API）
                # 这里返回None表示暂不实现，让系统继续正常流程
                return None

            return None
        except Exception as e:
            logger.error(f"维度合并分析时出错: {str(e)}")
            return None

    def _calculate_quality_threshold(self, text: str, analysis_type: str) -> float:
        """
        计算文本质量阈值（新降本增效方案10）
        根据文本复杂度决定分析深度
        """
        try:
            # 基础质量分数
            quality_score = 0.8

            # 根据文本长度调整
            text_length = len(text)
            if text_length < 1000:
                quality_score -= 0.2  # 短文本降低要求
            elif text_length > 10000:
                quality_score += 0.1  # 长文本提高要求

            # 根据文本复杂度调整
            # 检查句子长度变化
            sentences = text.split('。')
            if len(sentences) > 5:
                avg_sentence_length = sum(len(s) for s in sentences) / len(sentences)
                if avg_sentence_length < 20:
                    quality_score -= 0.1  # 简单句式降低要求
                elif avg_sentence_length > 50:
                    quality_score += 0.1  # 复杂句式提高要求

            # 检查词汇复杂度（简单实现）
            unique_chars = len(set(text))
            total_chars = len(text)
            if total_chars > 0:
                diversity_ratio = unique_chars / total_chars
                if diversity_ratio < 0.3:
                    quality_score -= 0.1  # 词汇单一降低要求
                elif diversity_ratio > 0.6:
                    quality_score += 0.1  # 词汇丰富提高要求

            # 限制在合理范围内
            quality_score = max(0.3, min(1.0, quality_score))

            logger.info(f"[质量阈值] 文本长度{text_length}，质量阈值{quality_score:.2f}")
            return quality_score

        except Exception as e:
            logger.error(f"计算质量阈值时出错: {str(e)}")
            return 0.8  # 默认值

    def _optimize_text_segments(self, text: str, analysis_type: str) -> str:
        """
        智能分段优化（新降本增效方案11）
        保留最重要的文本段落，减少不必要的内容
        """
        try:
            # 根据分析类型选择不同的优化策略
            if analysis_type in ["language_style", "tone_mood"]:
                # 语言风格分析：保留有代表性的段落
                return self._extract_representative_segments(text, target_ratio=0.6)
            elif analysis_type in ["structure", "paragraph_length"]:
                # 结构分析：保留开头、中间、结尾的关键段落
                return self._extract_structural_segments(text, target_ratio=0.7)
            elif analysis_type in ["character_development", "dialogue_narrative"]:
                # 人物分析：保留对话和人物描写段落
                return self._extract_character_segments(text, target_ratio=0.8)
            else:
                # 其他分析：均匀采样
                return self._extract_uniform_segments(text, target_ratio=0.7)

        except Exception as e:
            logger.error(f"智能分段优化时出错: {str(e)}")
            return text  # 出错时返回原文

    def _extract_representative_segments(self, text: str, target_ratio: float = 0.6) -> str:
        """提取有代表性的文本段落"""
        try:
            paragraphs = text.split('\n\n')
            if len(paragraphs) <= 3:
                return text

            # 计算目标段落数
            target_count = max(2, int(len(paragraphs) * target_ratio))

            # 选择段落：开头1个，结尾1个，中间均匀分布
            selected = []
            selected.append(paragraphs[0])  # 开头

            if target_count > 2:
                # 中间段落
                middle_count = target_count - 2
                step = max(1, (len(paragraphs) - 2) // middle_count)
                for i in range(1, len(paragraphs) - 1, step):
                    if len(selected) < target_count - 1:
                        selected.append(paragraphs[i])

            selected.append(paragraphs[-1])  # 结尾

            result = '\n\n'.join(selected)
            logger.info(f"[分段优化] 代表性段落：从{len(paragraphs)}段选择{len(selected)}段，压缩率{(1-len(result)/len(text))*100:.1f}%")
            return result

        except Exception as e:
            logger.error(f"提取代表性段落时出错: {str(e)}")
            return text

    def _extract_structural_segments(self, text: str, target_ratio: float = 0.7) -> str:
        """提取结构关键段落"""
        try:
            paragraphs = text.split('\n\n')
            if len(paragraphs) <= 3:
                return text

            # 保留开头25%、中间50%、结尾25%的内容
            total_len = len(paragraphs)
            start_count = max(1, int(total_len * 0.25))
            middle_count = max(1, int(total_len * 0.5))
            end_count = max(1, int(total_len * 0.25))

            # 调整以符合目标比例
            total_target = int(total_len * target_ratio)
            if start_count + middle_count + end_count > total_target:
                # 按比例缩减
                ratio = total_target / (start_count + middle_count + end_count)
                start_count = max(1, int(start_count * ratio))
                middle_count = max(1, int(middle_count * ratio))
                end_count = max(1, int(end_count * ratio))

            selected = []
            # 开头段落
            selected.extend(paragraphs[:start_count])
            # 中间段落
            middle_start = total_len // 4
            middle_end = middle_start + middle_count
            selected.extend(paragraphs[middle_start:middle_end])
            # 结尾段落
            selected.extend(paragraphs[-end_count:])

            result = '\n\n'.join(selected)
            logger.info(f"[分段优化] 结构段落：选择{len(selected)}段，压缩率{(1-len(result)/len(text))*100:.1f}%")
            return result

        except Exception as e:
            logger.error(f"提取结构段落时出错: {str(e)}")
            return text

    def _extract_character_segments(self, text: str, target_ratio: float = 0.8) -> str:
        """提取人物相关段落"""
        try:
            paragraphs = text.split('\n\n')
            if len(paragraphs) <= 3:
                return text

            # 寻找包含对话或人物描写的段落
            character_paragraphs = []
            other_paragraphs = []

            for para in paragraphs:
                # 简单检测：包含引号、人物动作词汇等
                if ('「' in para or '」' in para or '"' in para or
                    '说' in para or '道' in para or '想' in para or
                    '看' in para or '听' in para):
                    character_paragraphs.append(para)
                else:
                    other_paragraphs.append(para)

            # 计算目标段落数
            target_count = max(2, int(len(paragraphs) * target_ratio))

            # 优先选择人物相关段落
            selected = character_paragraphs[:target_count]

            # 如果不够，补充其他段落
            if len(selected) < target_count:
                remaining = target_count - len(selected)
                selected.extend(other_paragraphs[:remaining])

            result = '\n\n'.join(selected)
            logger.info(f"[分段优化] 人物段落：选择{len(selected)}段（人物相关{len(character_paragraphs)}段），压缩率{(1-len(result)/len(text))*100:.1f}%")
            return result

        except Exception as e:
            logger.error(f"提取人物段落时出错: {str(e)}")
            return text

    def _extract_uniform_segments(self, text: str, target_ratio: float = 0.7) -> str:
        """均匀提取段落"""
        try:
            paragraphs = text.split('\n\n')
            if len(paragraphs) <= 3:
                return text

            # 计算目标段落数
            target_count = max(2, int(len(paragraphs) * target_ratio))

            # 均匀采样
            step = len(paragraphs) / target_count
            selected_indices = [int(i * step) for i in range(target_count)]

            selected = [paragraphs[i] for i in selected_indices if i < len(paragraphs)]

            result = '\n\n'.join(selected)
            logger.info(f"[分段优化] 均匀段落：选择{len(selected)}段，压缩率{(1-len(result)/len(text))*100:.1f}%")
            return result

        except Exception as e:
            logger.error(f"均匀提取段落时出错: {str(e)}")
            return text

    def _get_analysis_stop_words(self, analysis_type: str) -> list:
        """
        获取分析类型特定的停止词（新降本增效方案12）
        减少不必要的输出内容
        """
        try:
            stop_words_map = {
                "language_style": ["总结", "综上所述", "最后", "结论"],
                "rhythm_pacing": ["总体而言", "整体来看", "综合分析"],
                "structure": ["最终", "总的来说", "概括而言"],
                "character_development": ["总结人物", "人物总结", "综合评价"],
                "dialogue_narrative": ["对话总结", "叙述总结", "综合来看"]
            }

            return stop_words_map.get(analysis_type, [])
        except Exception as e:
            logger.error(f"获取停止词时出错: {str(e)}")
            return []

    def _calculate_analysis_complexity(self, text: str, analysis_type: str) -> float:
        """
        计算分析复杂度（新降本增效方案14）
        根据文本和分析类型的复杂度调整token使用
        """
        try:
            complexity_score = 0.5  # 基础复杂度

            # 文本复杂度因素
            text_length = len(text)

            # 长度因素
            if text_length < 500:
                complexity_score -= 0.2
            elif text_length > 5000:
                complexity_score += 0.2

            # 句子复杂度
            sentences = text.split('。')
            if len(sentences) > 3:
                avg_sentence_length = sum(len(s) for s in sentences) / len(sentences)
                if avg_sentence_length > 30:
                    complexity_score += 0.1
                elif avg_sentence_length < 15:
                    complexity_score -= 0.1

            # 分析类型复杂度
            complex_analysis_types = ["character_development", "theme_analysis", "narrative_techniques"]
            simple_analysis_types = ["paragraph_length", "sentence_variation", "word_count"]

            if analysis_type in complex_analysis_types:
                complexity_score += 0.2
            elif analysis_type in simple_analysis_types:
                complexity_score -= 0.2

            # 词汇多样性
            unique_chars = len(set(text))
            total_chars = len(text)
            if total_chars > 0:
                diversity_ratio = unique_chars / total_chars
                if diversity_ratio > 0.5:
                    complexity_score += 0.1
                elif diversity_ratio < 0.3:
                    complexity_score -= 0.1

            # 限制在合理范围
            complexity_score = max(0.1, min(1.0, complexity_score))

            logger.info(f"[复杂度分析] 文本长度{text_length}，分析类型{analysis_type}，复杂度{complexity_score:.2f}")
            return complexity_score

        except Exception as e:
            logger.error(f"计算分析复杂度时出错: {str(e)}")
            return 0.5  # 默认中等复杂度

    def _apply_batch_optimization(self, texts: list, analysis_types: list, prompt_template: str = "simplified") -> dict:
        """
        批量分析优化（新降本增效方案15）
        将多个小分析合并为一次API调用
        """
        try:
            if not texts or not analysis_types or len(texts) != len(analysis_types):
                return None

            # 只在精简版模式下启用批量优化
            if prompt_template != "simplified":
                return None

            # 检查是否适合批量处理
            total_length = sum(len(text) for text in texts)
            if total_length > 10000:  # 总长度太大，不适合批量
                return None

            # 构建批量分析提示词
            batch_prompt = "请分析以下多个文本片段：\n\n"

            for i, (text, analysis_type) in enumerate(zip(texts, analysis_types)):
                batch_prompt += f"## 文本{i+1} - {analysis_type}分析\n"
                batch_prompt += f"文本内容：{text[:1000]}\n"  # 限制每个文本长度
                batch_prompt += f"请进行{analysis_type}分析\n\n"

            batch_prompt += "请按照上述顺序，为每个文本提供简洁的分析结果。"

            logger.info(f"[批量优化] 合并{len(texts)}个分析任务，总长度{total_length}字符")

            # 返回批量提示词，实际API调用由调用方处理
            return {
                "batch_prompt": batch_prompt,
                "batch_count": len(texts),
                "total_length": total_length
            }

        except Exception as e:
            logger.error(f"批量优化时出错: {str(e)}")
            return None

    def _optimize_prompt_structure(self, prompt: str, analysis_type: str) -> str:
        """
        优化提示词结构（新降本增效方案16）
        重组提示词以减少冗余，提高效率
        """
        try:
            # 移除重复的指令
            lines = prompt.split('\n')
            unique_lines = []
            seen_patterns = set()

            for line in lines:
                # 检查是否是重复的指令模式
                line_pattern = self._extract_instruction_pattern(line)
                if line_pattern not in seen_patterns:
                    unique_lines.append(line)
                    seen_patterns.add(line_pattern)

            optimized_prompt = '\n'.join(unique_lines)

            # 简化冗长的说明
            optimized_prompt = self._simplify_verbose_instructions(optimized_prompt)

            # 移除不必要的示例（在精简版中）
            optimized_prompt = self._remove_unnecessary_examples(optimized_prompt, analysis_type)

            reduction_ratio = (len(prompt) - len(optimized_prompt)) / len(prompt) * 100
            if reduction_ratio > 5:  # 只有显著减少时才记录
                logger.info(f"[提示词优化] 结构优化减少{reduction_ratio:.1f}%，从{len(prompt)}字符减少到{len(optimized_prompt)}字符")

            return optimized_prompt

        except Exception as e:
            logger.error(f"优化提示词结构时出错: {str(e)}")
            return prompt

    def _extract_instruction_pattern(self, line: str) -> str:
        """提取指令模式，用于去重"""
        try:
            # 简化指令到核心模式
            line = line.strip()
            if not line:
                return ""

            # 移除数字、标点等，保留核心指令
            import re
            pattern = re.sub(r'[0-9\.\-\s]+', '', line)
            pattern = re.sub(r'[，。！？：；""''（）【】]', '', pattern)

            return pattern[:20]  # 只取前20个字符作为模式
        except:
            return line[:20]

    def _simplify_verbose_instructions(self, prompt: str) -> str:
        """简化冗长的指令"""
        try:
            # 替换冗长的表达为简洁版本
            replacements = {
                "请详细分析并说明": "请分析",
                "请仔细观察并详细描述": "请描述",
                "请深入分析并提供详细的": "请分析",
                "请全面分析以下内容": "请分析以下内容",
                "请进行深入细致的分析": "请分析",
                "请提供详尽的分析报告": "请分析",
                "请从多个角度进行全面分析": "请多角度分析"
            }

            for verbose, simple in replacements.items():
                prompt = prompt.replace(verbose, simple)

            return prompt
        except Exception as e:
            logger.error(f"简化冗长指令时出错: {str(e)}")
            return prompt

    def _remove_unnecessary_examples(self, prompt: str, analysis_type: str) -> str:
        """移除不必要的示例"""
        try:
            # 在精简版中，移除部分示例以减少token
            lines = prompt.split('\n')
            filtered_lines = []
            in_example_section = False
            example_count = 0

            for line in lines:
                # 检测示例开始
                if '示例' in line or '例如' in line or '比如' in line:
                    in_example_section = True
                    example_count += 1

                    # 只保留第一个示例
                    if example_count <= 1:
                        filtered_lines.append(line)
                    continue

                # 检测示例结束
                if in_example_section and (line.strip() == '' or line.startswith('##') or line.startswith('请')):
                    in_example_section = False

                # 决定是否保留这一行
                if not in_example_section or example_count <= 1:
                    filtered_lines.append(line)

            return '\n'.join(filtered_lines)
        except Exception as e:
            logger.error(f"移除不必要示例时出错: {str(e)}")
            return prompt

    async def analyze_chapter_dimension(self, novel_id, chapter_id, dimension, previous_analyses=None):
        """
        分析小说章节的特定维度，强化章节间的连贯性和递进关系

        Args:
            novel_id: 小说ID
            chapter_id: 章节ID
            dimension: 分析维度
            previous_analyses: 前序章节的分析结果列表

        Returns:
            分析结果
        """
        try:
            # 获取章节内容
            chapter_content = await self.get_chapter_content(novel_id, chapter_id)
            if not chapter_content:
                return {"error": "无法获取章节内容"}

            # 获取章节标题
            chapter_info = await self.get_chapter_info(novel_id, chapter_id)
            chapter_title = chapter_info.get("title", f"第{chapter_id}章")
            chapter_number = chapter_info.get("chapter_number", chapter_id)

            # 获取更多的前序章节分析结果，以增强连贯性
            if not previous_analyses:
                # 如果没有提供前序章节分析，尝试获取更多前序章节的分析结果
                logger.info(f"尝试获取前序章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
                previous_analyses = await self.get_previous_chapter_analyses(
                    novel_id=novel_id,
                    chapter_id=chapter_id,
                    dimension=dimension,
                    limit=3  # 获取前3个章节的分析结果，增加连贯性
                )

                # 记录获取到的前序章节分析结果
                if previous_analyses:
                    logger.info(f"成功获取 {len(previous_analyses)} 个前序章节分析结果")
                    for i, prev in enumerate(previous_analyses):
                        logger.info(f"前序章节 {i+1}: chapter_id={prev.get('chapter_id')}, chapter_title={prev.get('chapter_title')}, content长度={len(prev.get('content', ''))}, reasoning_content长度={len(prev.get('reasoning_content', ''))}")
                else:
                    logger.warning(f"未能获取到前序章节分析结果")

            # 获取小说基本信息，为分析提供更多上下文
            novel_info = await self.get_novel_info(novel_id)
            novel_title = novel_info.get("title", "未知小说")

            # 构建增强的提示词，强调章节间的连贯性和递进关系
            prompt = await self.build_chapter_analysis_prompt(
                dimension,
                chapter_content,
                chapter_title,
                previous_analyses,
                novel_title=novel_title,
                chapter_number=chapter_number
            )

            # 在日志中记录分析的上下文信息
            logger.info(f"开始分析 '{novel_title}' 的第 {chapter_number} 章 '{chapter_title}' 的 {dimension} 维度")
            if previous_analyses:
                logger.info(f"分析包含 {len(previous_analyses)} 个前序章节的连贯性和递进关系")

            # 调用API进行分析
            response = await self.analyze_with_api(prompt)

            if not response:
                return {"error": "API分析失败"}

            # 增强分析结果，确保包含章节间的连贯性分析
            if response and "content" in response:
                # 检查分析结果是否包含章节间联系分析
                if "章节间联系" not in response["content"] and previous_analyses:
                    # 如果没有包含章节间联系分析，添加一个提示
                    response["content"] += "\n\n## 章节间联系与发展提示\n本章节应与前序章节形成连贯的分析体系，请参考前序章节分析结果，关注情节、人物、主题和写作手法的延续与发展。"

            return response

        except Exception as e:
            logger.error(f"分析章节维度时出错: {str(e)}")
            return {"error": f"分析章节维度时出错: {str(e)}"}

    async def get_chapter_content(self, novel_id, chapter_id):
        """获取章节内容"""
        try:
            from src.models.chapter import Chapter
            from src.db.connection import Session

            session = Session()
            try:
                chapter = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.id == chapter_id
                ).first()

                if not chapter:
                    logger.error(f"找不到章节: novel_id={novel_id}, chapter_id={chapter_id}")
                    return None

                return chapter.content
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取章节内容时出错: {str(e)}")
            return None

    async def get_chapter_info(self, novel_id, chapter_id):
        """获取章节信息"""
        try:
            from src.models.chapter import Chapter
            from src.db.connection import Session

            session = Session()
            try:
                chapter = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.id == chapter_id
                ).first()

                if not chapter:
                    logger.error(f"找不到章节: novel_id={novel_id}, chapter_id={chapter_id}")
                    return {}

                return {
                    "id": chapter.id,
                    "title": chapter.title,
                    "chapter_number": chapter.chapter_number,
                    "word_count": len(chapter.content) if chapter.content else 0
                }
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取章节信息时出错: {str(e)}")
            return {}

    async def get_novel_info(self, novel_id):
        """获取小说信息"""
        try:
            from src.models.novel import Novel
            from src.db.connection import Session

            session = Session()
            try:
                novel = session.query(Novel).filter(Novel.id == novel_id).first()

                if not novel:
                    logger.error(f"找不到小说: novel_id={novel_id}")
                    return {}

                return {
                    "id": novel.id,
                    "title": novel.title,
                    "author": novel.author,
                    "word_count": novel.word_count
                }
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取小说信息时出错: {str(e)}")
            return {}

    async def get_previous_chapter_analyses(self, novel_id, chapter_id, dimension, limit=3):
        """获取前序章节的分析结果"""
        try:
            from src.models.chapter import Chapter
            from src.models.chapter_analysis_result import ChapterAnalysisResult
            from src.db.connection import Session

            session = Session()
            try:
                # 获取当前章节信息
                current_chapter = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.id == chapter_id
                ).first()

                if not current_chapter:
                    logger.error(f"找不到当前章节: novel_id={novel_id}, chapter_id={chapter_id}")
                    return []

                # 获取前序章节
                previous_chapters = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.chapter_number < current_chapter.chapter_number
                ).order_by(Chapter.chapter_number.desc()).limit(limit).all()

                if not previous_chapters:
                    logger.info(f"当前章节 {chapter_id} 是第一章或前面没有章节")
                    return []

                # 获取前序章节的分析结果
                previous_analyses = []
                for prev_chapter in previous_chapters:
                    analysis_result = session.query(ChapterAnalysisResult).filter(
                        ChapterAnalysisResult.novel_id == novel_id,
                        ChapterAnalysisResult.chapter_id == prev_chapter.id,
                        ChapterAnalysisResult.dimension == dimension
                    ).first()

                    # 直接从ChapterAnalysisResult对象获取分析结果
                    if analysis_result:
                        try:
                            # 直接使用content和reasoning_content字段
                            content = analysis_result.content if hasattr(analysis_result, 'content') else ""
                            reasoning_content = analysis_result.reasoning_content if hasattr(analysis_result, 'reasoning_content') else ""

                            # 记录字段信息，用于调试
                            logger.debug(f"ChapterAnalysisResult对象字段: {dir(analysis_result)}")
                            logger.debug(f"获取到的content长度: {len(content)}, reasoning_content长度: {len(reasoning_content)}")

                            # 确保content和reasoning_content不为空
                            content = content or ""
                            reasoning_content = reasoning_content or ""

                            # 只有当content不为空时才添加到前序分析结果中
                            if content.strip():
                                previous_analyses.append({
                                    "chapter_id": prev_chapter.id,
                                    "chapter_number": prev_chapter.chapter_number,
                                    "chapter_title": prev_chapter.title or f"第{prev_chapter.chapter_number}章",
                                    "content": content,
                                    "reasoning_content": reasoning_content
                                })
                                logger.info(f"成功获取章节 {prev_chapter.id} 的 {dimension} 维度分析结果，内容长度: {len(content)} 字符")
                            else:
                                logger.warning(f"章节 {prev_chapter.id} 的 {dimension} 维度分析结果内容为空，跳过")
                        except Exception as e:
                            logger.error(f"处理章节 {prev_chapter.id} 的分析结果时出错: {str(e)}")
                    else:
                        logger.warning(f"章节 {prev_chapter.id} 没有 {dimension} 维度的分析结果")

                logger.info(f"成功获取 {len(previous_analyses)} 个前序章节的分析结果")
                return previous_analyses
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取前序章节分析结果时出错: {str(e)}")
            return []

    async def build_chapter_analysis_prompt(self, dimension, chapter_content, chapter_title, previous_analyses=None, novel_title="", chapter_number=0):
        """构建章节分析提示词"""
        # 构建前序章节分析结果的摘要
        previous_analyses_summary = ""
        if previous_analyses:
            previous_analyses_summary = "## 前序章节分析摘要：\n\n"
            for i, prev_analysis in enumerate(previous_analyses):
                prev_chapter_title = prev_analysis.get("chapter_title", f"第{prev_analysis.get('chapter_number')}章")
                content = prev_analysis.get("content", "")
                reasoning_content = prev_analysis.get("reasoning_content", "")

                # 提取分析内容的关键点
                key_points = []

                # 尝试提取主要分析点
                analysis_points = re.findall(r'[一二三四五六七八九十]、([^一二三四五六七八九十\n]+)', content)
                if analysis_points:
                    key_points.extend([f"• {point.strip()}" for point in analysis_points[:3]])

                # 尝试提取小标题
                subtitles = re.findall(r'(?:###|[0-9]+\.) ([^\n]+)', content)
                if subtitles:
                    key_points.extend([f"• {subtitle.strip()}" for subtitle in subtitles[:3]])

                # 如果没有提取到结构化内容，使用前500字
                if not key_points:
                    content_summary = content[:500] + "..." if len(content) > 500 else content
                    previous_analyses_summary += f"### {prev_chapter_title} 分析摘要：\n{content_summary}\n\n"
                else:
                    # 使用提取的关键点
                    previous_analyses_summary += f"### {prev_chapter_title} 分析要点：\n"
                    for point in key_points:
                        previous_analyses_summary += f"{point}\n"
                    previous_analyses_summary += "\n"

                # 添加推理过程摘要
                if reasoning_content:
                    reasoning_summary = reasoning_content[:300] + "..." if len(reasoning_content) > 300 else reasoning_content
                    previous_analyses_summary += f"### {prev_chapter_title} 推理过程摘要：\n{reasoning_summary}\n\n"

            # 添加明确的连贯性分析指导
            previous_analyses_summary += """### 重要提示：
在分析本章节时，请特别注意与前序章节的连贯性和递进关系。你的分析必须：

1. 明确指出本章节如何延续和发展前序章节的情节、人物和主题
2. 分析本章节相比前序章节的新变化和发展
3. 识别贯穿各章节的核心元素和它们的演变
4. 分析作者在本章节使用的写作手法如何与前序章节形成呼应或对比
5. 评估本章节在整体故事发展中的作用和地位

你的分析应该形成一个连贯、递进的分析体系，而不是将本章节孤立对待。
"""

        # 添加章节间联系分析部分
        chapter_connection_section = ""
        if previous_analyses:
            chapter_connection_section = """
## 章节间联系与发展：
（这里详细分析本章节与前序章节的联系、变化和发展，必须明确指出本章节是如何延续、发展或转变前序章节的元素。分析必须具体且有深度，不能泛泛而谈。）

### 情节连贯性分析：
（分析本章节的情节如何承接前序章节，有哪些情节线索的延续和发展）

### 人物发展分析：
（分析主要人物在本章节相比前序章节有哪些变化和发展）

### 主题深化分析：
（分析本章节如何深化或转变前序章节建立的主题）

### 写作手法递进：
（分析本章节在写作手法上相比前序章节有哪些延续、变化或提升）
"""

        # 增强版提示模板
        enhanced_prompt_template = f"""你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说《{novel_title}》的第{chapter_number}章《{chapter_title}》进行详细的{dimension}分析。

请按照以下格式输出分析结果：

## 分析思路说明：
1. [分析方法1]：简要说明你将如何分析这个维度的第一个方面
2. [分析方法2]：简要说明你将如何分析这个维度的第二个方面
3. [分析方法3]：简要说明你将如何分析这个维度的第三个方面
4. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
5. 必须叙述原文内容：详细描述章节中的主要内容包括不限于场景、事件、对话和人物心理活动
6. 不限字数不做限制：分析结果越详细越好，不要因为字数限制而简化分析
7. 必须承接上一章节同维度的分析结果和推理过程：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展
8. 语言必须通俗易懂：使用清晰、简洁的语言表达，避免过多专业术语
...（根据具体维度可以有更多分析方法）

## 详细{dimension}分析：
（这里是详细的分析内容，包括多个小节和具体分析）

{chapter_connection_section}

## 重要分析指导：
1. 请使用通俗易懂的语言进行分析，避免过多专业术语和学术化表达
2. 每个分析点都应包含5-7个从文本中提取的具体例子（直接引用原文）
3. 对这些例子进行简明的解释说明，说明它们如何体现你的分析观点
4. 评估这些特点对读者体验的影响
5. 分析结论应该具体明确，避免空泛的评价
6. 绝对避免使用晦涩难懂的学术术语和概念，如"时空错位理论"、"系统干预层"等抽象概念
7. 使用日常生活中常见的比喻和例子来解释复杂概念
8. 分析应该像是在与普通读者交流，而不是学术论文
9. 在分析中要体现章节间的联系和发展脉络，不要将本章节孤立对待

请确保分析思路部分简明扼要，列出3-5点关键分析方法。详细分析部分则应该全面、深入、有条理，同时保持语言通俗易懂，多举具体例子。最终的分析结果应该是任何高中学历的读者都能轻松理解的内容。

{previous_analyses_summary if previous_analyses else ''}

文本：
{chapter_content}
"""

        return enhanced_prompt_template

    def _generate_cache_key(self, text: str, analysis_type: str, prompt_template: str) -> str:
        """
        生成缓存键（降本增效方案6）

        Args:
            text: 分析文本
            analysis_type: 分析类型
            prompt_template: 提示词模板

        Returns:
            缓存键
        """
        import hashlib

        # 对文本进行简化处理，提取关键特征
        text_features = self._extract_text_features(text)

        # 生成缓存键
        cache_content = f"{analysis_type}_{prompt_template}_{text_features}"
        cache_key = hashlib.md5(cache_content.encode('utf-8')).hexdigest()

        return f"simplified_cache_{cache_key}"

    def _extract_text_features(self, text: str) -> str:
        """
        提取文本特征用于缓存匹配

        Args:
            text: 原始文本

        Returns:
            文本特征字符串
        """
        # 计算文本长度
        text_length = len(text)

        # 提取关键词（简单实现）
        import re
        words = re.findall(r'[\u4e00-\u9fff]+', text)  # 提取中文词汇
        word_count = len(words)

        # 计算文本哈希（前1000字符）
        import hashlib
        text_sample = text[:1000] if len(text) > 1000 else text
        text_hash = hashlib.md5(text_sample.encode('utf-8')).hexdigest()[:8]

        return f"len_{text_length}_words_{word_count}_hash_{text_hash}"

    def _get_cached_result(self, cache_key: str) -> dict:
        """
        获取缓存结果

        Args:
            cache_key: 缓存键

        Returns:
            缓存的分析结果，如果没有则返回None
        """
        try:
            import os
            import json
            from datetime import datetime, timedelta

            # 缓存目录
            cache_dir = os.path.join(os.path.dirname(__file__), '..', 'cache', 'simplified')
            os.makedirs(cache_dir, exist_ok=True)

            cache_file = os.path.join(cache_dir, f"{cache_key}.json")

            if not os.path.exists(cache_file):
                return None

            # 检查缓存是否过期（24小时）
            file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
            if datetime.now() - file_time > timedelta(hours=24):
                os.remove(cache_file)
                logger.info(f"[降本增效] 缓存已过期，已删除: {cache_key}")
                return None

            # 读取缓存
            with open(cache_file, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)

            logger.info(f"[降本增效] 成功读取缓存: {cache_key}")
            return cached_data

        except Exception as e:
            logger.error(f"读取缓存时出错: {str(e)}")
            return None

    def _save_cached_result(self, cache_key: str, result: dict) -> None:
        """
        保存分析结果到缓存

        Args:
            cache_key: 缓存键
            result: 分析结果
        """
        try:
            import os
            import json

            # 缓存目录
            cache_dir = os.path.join(os.path.dirname(__file__), '..', 'cache', 'simplified')
            os.makedirs(cache_dir, exist_ok=True)

            cache_file = os.path.join(cache_dir, f"{cache_key}.json")

            # 添加缓存元数据
            cached_data = {
                **result,
                "cached_at": datetime.now().isoformat(),
                "cache_key": cache_key
            }

            # 保存到文件
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cached_data, f, ensure_ascii=False, indent=2)

            logger.info(f"[降本增效] 成功保存缓存: {cache_key}")

        except Exception as e:
            logger.error(f"保存缓存时出错: {str(e)}")

    async def analyze_with_api(self, prompt):
        """调用API进行分析"""
        try:
            # 使用现有的analyze_text方法
            result = self.analyze_text(prompt, "chapter_analysis")
            return result
        except Exception as e:
            logger.error(f"调用API分析时出错: {str(e)}")
            return None

    def _get_previous_chapter_analysis(self, novel_id: int, chapter_id: int, dimension: str, limit: int = 2) -> list:
        """
        获取前面章节的分析结果，用于章节间信息联通

        Args:
            novel_id: 小说ID
            chapter_id: 当前章节ID
            dimension: 分析维度
            limit: 获取的前序章节数量

        Returns:
            前序章节的分析结果列表
        """
        if not novel_id or not chapter_id:
            logger.info(f"无法获取前序章节分析结果：缺少小说ID或章节ID")
            return []

        try:
            from src.models.chapter import Chapter
            from src.models.chapter_analysis_result import ChapterAnalysisResult
            from src.db.connection import Session

            with Session() as session:
                # 获取当前章节的信息
                current_chapter = session.query(Chapter).filter(Chapter.id == chapter_id, Chapter.novel_id == novel_id).first()
                if not current_chapter:
                    logger.error(f"获取前序章节分析结果失败：当前章节不存在 chapter_id={chapter_id}")
                    return []

                # 获取前面的章节
                previous_chapters = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.chapter_number < current_chapter.chapter_number
                ).order_by(Chapter.chapter_number.desc()).limit(limit).all()

                if not previous_chapters:
                    logger.info(f"当前章节 {chapter_id} 是第一章或前面没有章节")
                    return []

                # 获取前面章节的分析结果
                previous_analyses = []
                for prev_chapter in previous_chapters:
                    analysis_result = session.query(ChapterAnalysisResult).filter(
                        ChapterAnalysisResult.novel_id == novel_id,
                        ChapterAnalysisResult.chapter_id == prev_chapter.id,
                        ChapterAnalysisResult.dimension == dimension
                    ).first()

                    if analysis_result:
                        try:
                            # 直接从对象获取content字段
                            content = ""
                            if hasattr(analysis_result, "content"):
                                content = analysis_result.content

                            previous_analyses.append({
                                "chapter_id": prev_chapter.id,
                                "chapter_number": prev_chapter.chapter_number,
                                "chapter_title": prev_chapter.title or f"第{prev_chapter.chapter_number}章",
                                "content": content
                            })
                        except Exception as e:
                            logger.error(f"解析前序章节分析结果时出错: {str(e)}")

                logger.info(f"成功获取 {len(previous_analyses)} 个前序章节的分析结果")
                return previous_analyses
        except Exception as e:
            logger.error(f"获取前序章节分析结果时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _create_analysis_prompt(self, text: str, analysis_type: str, prompt_template: str = "default") -> str:
        """
        Create an analysis prompt based on the analysis type and template.

        Args:
            text: Text to analyze.
            analysis_type: Type of analysis to perform.
            prompt_template: Prompt template to use (default or simplified).

        Returns:
            Analysis prompt.
        """
        # 获取当前分析的小说ID和章节ID
        novel_id = getattr(self, 'current_novel_id', None)
        chapter_id = getattr(self, 'current_chapter_id', None)

        # 获取前序章节的分析结果
        previous_analyses = []
        if novel_id and chapter_id:
            previous_analyses = self._get_previous_chapter_analysis(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=analysis_type,
                limit=2  # 获取前2个章节的分析结果
            )

        # 构建前序章节分析结果的摘要
        previous_analyses_summary = ""
        if previous_analyses:
            previous_analyses_summary = "## 前序章节分析摘要：\n\n"
            for i, prev_analysis in enumerate(previous_analyses):
                chapter_title = prev_analysis.get("chapter_title", f"第{prev_analysis.get('chapter_number')}章")
                content = prev_analysis.get("content", "")

                # 提取分析内容的关键点
                # 尝试提取结构化内容
                key_points = []

                # 尝试提取主要分析点
                analysis_points = re.findall(r'[一二三四五六七八九十]、([^一二三四五六七八九十\n]+)', content)
                if analysis_points:
                    key_points.extend([f"• {point.strip()}" for point in analysis_points[:3]])

                # 尝试提取小标题
                subtitles = re.findall(r'(?:###|[0-9]+\.) ([^\n]+)', content)
                if subtitles:
                    key_points.extend([f"• {subtitle.strip()}" for subtitle in subtitles[:3]])

                # 如果没有提取到结构化内容，使用前500字
                if not key_points:
                    content_summary = content[:500] + "..." if len(content) > 500 else content
                    previous_analyses_summary += f"### {chapter_title} 分析摘要：\n{content_summary}\n\n"
                else:
                    # 使用提取的关键点
                    previous_analyses_summary += f"### {chapter_title} 分析要点：\n"
                    for point in key_points:
                        previous_analyses_summary += f"{point}\n"
                    previous_analyses_summary += "\n"

            # 添加明确的连贯性分析指导
            previous_analyses_summary += """### 重要提示：
在分析本章节时，请特别注意与前序章节的连贯性和递进关系。你的分析必须：

1. 明确指出本章节如何延续和发展前序章节的情节、人物和主题
2. 分析本章节相比前序章节的新变化和发展
3. 识别贯穿各章节的核心元素和它们的演变
4. 分析作者在本章节使用的写作手法如何与前序章节形成呼应或对比
5. 评估本章节在整体故事发展中的作用和地位

你的分析应该形成一个连贯、递进的分析体系，而不是将本章节孤立对待。
"""

            logger.info(f"成功构建结构化的前序章节分析摘要，包含 {len(previous_analyses)} 个章节")

        # 基础提示模板
        base_prompt = "你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说文本进行详细的{analysis_type}分析。"

        # 添加前序章节分析指导
        chapter_connection_section = ""
        if previous_analyses:
            chapter_connection_section = """
## 章节间联系与发展：
（这里详细分析本章节与前序章节的联系、变化和发展，必须明确指出本章节是如何延续、发展或转变前序章节的元素。分析必须具体且有深度，不能泛泛而谈。）

### 情节连贯性分析：
（分析本章节的情节如何承接前序章节，有哪些情节线索的延续和发展）

### 人物发展分析：
（分析主要人物在本章节相比前序章节有哪些变化和发展）

### 主题深化分析：
（分析本章节如何深化或转变前序章节建立的主题）

### 写作手法递进：
（分析本章节在写作手法上相比前序章节有哪些延续、变化或提升）
"""

        # 根据提示词模板选择不同的提示模板
        if prompt_template == "simplified":
            # 精简版提示模板，保持要素齐全但更简洁
            enhanced_prompt_template = """你是一位文学分析专家，请对以下小说文本进行{analysis_type}分析。

请按照以下格式输出分析结果：

## 分析思路说明：
1. [分析方法1]：简要说明分析方法
2. [分析方法2]：简要说明分析方法
3. [分析方法3]：简要说明分析方法
4. 基于原文：分析必须基于文本内容
5. 叙述原文：描述章节中的主要内容
6. 承接前章：将前序章节分析作为基础
7. 通俗易懂：使用简洁语言表达

## 详细{analysis_type}分析：
（详细分析内容）

{chapter_connection_section}

## 分析指导：
1. 使用通俗易懂的语言
2. 提供具体例子
3. 解释例子如何体现分析观点
4. 评估对读者体验的影响
5. 避免晦涩难懂的术语
6. 使用日常比喻解释概念
7. 体现章节间的连贯性

请确保分析思路简明扼要，详细分析部分全面有条理，语言通俗易懂，多举具体例子。"""
        else:
            # 默认提示模板，包含更详细的输出格式要求和通俗易懂的指导
            enhanced_prompt_template = """你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说文本进行详细的{analysis_type}分析。

请按照以下格式输出分析结果：

## 分析思路说明：
1. [分析方法1]：简要说明你将如何分析这个维度的第一个方面
2. [分析方法2]：简要说明你将如何分析这个维度的第二个方面
3. [分析方法3]：简要说明你将如何分析这个维度的第三个方面
4. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
5. 必须叙述原文内容：详细描述章节中的主要内容包括不限于场景、事件、对话和人物心理活动
6. 不限字数不做限制：分析结果越详细越好，不要因为字数限制而简化分析
7. 必须承接上一章节同维度的分析结果和推理过程：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展
8. 语言必须通俗易懂：使用清晰、简洁的语言表达，避免过多专业术语
...（根据具体维度可以有更多分析方法）

## 详细{analysis_type}分析：
（这里是详细的分析内容，包括多个小节和具体分析）

{chapter_connection_section}

## 重要分析指导：
1. 请使用通俗易懂的语言进行分析，避免过多专业术语和学术化表达
2. 每个分析点都应包含5-7个从文本中提取的具体例子（直接引用原文）
3. 对这些例子进行简明的解释说明，说明它们如何体现你的分析观点
4. 评估这些特点对读者体验的影响
5. 分析结论应该具体明确，避免空泛的评价
6. 绝对避免使用晦涩难懂的学术术语和概念，如"时空错位理论"、"系统干预层"等抽象概念
7. 使用日常生活中常见的比喻和例子来解释复杂概念
8. 分析应该像是在与普通读者交流，而不是学术论文
9. 在分析中要体现章节间的联系和发展脉络，不要将本章节孤立对待

请确保分析思路部分简明扼要，列出3-5点关键分析方法。详细分析部分则应该全面、深入、有条理，同时保持语言通俗易懂，多举具体例子。最终的分析结果应该是任何高中学历的读者都能轻松理解的内容。"""

        # 填充模板
        enhanced_prompt = enhanced_prompt_template.format(
            analysis_type=analysis_type,
            chapter_connection_section=chapter_connection_section
        )

        # 不同分析维度的提示词
        prompts = {
            "language_style": (
                f"{enhanced_prompt} 摘要: 请对以下小说文本进行"
                f"{'语言风格分析' if prompt_template == 'simplified' else '逐字逐句的详细的语言风格分析'}。\n\n"
                f"{'重要要求：' if prompt_template == 'simplified' else '重要补充要求（必须首先执行）：'}\n"
                f"1. {'基于原文：分析必须基于文本内容' if prompt_template == 'simplified' else '必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测'}\n"
                f"2. {'叙述原文：描述章节中的主要内容' if prompt_template == 'simplified' else '必须叙述原文内容：详细描述章节中的场景、事件、对话和人物心理活动'}\n"
                f"3. {'不限字数：分析越详细越好' if prompt_template == 'simplified' else '不限字数不做限制：分析结果越详细越好，不要因为字数限制而简化分析'}\n"
                f"4. {'承接前章：将前序章节分析作为基础' if prompt_template == 'simplified' else '必须承接上一章节同维度的分析结果和推理过程：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展'}\n"
                f"5. {'通俗易懂：使用简洁语言表达' if prompt_template == 'simplified' else '语言必须通俗易懂：使用清晰、简洁的语言表达，避免过多专业术语'}\n\n"
                f"请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n"
                f"{previous_analyses_summary if previous_analyses else ''}\n\n"
                f"文本：\n{text}"
            ),
            "rhythm_pacing": f"{enhanced_prompt} 摘要: "
                f"{'对以下小说文本进行节奏控制分析' if prompt_template == 'simplified' else '重点对以下小说文本进行节奏控制分析。包括段落长度、句式节奏、情节发展速度等方面'}"
                f"。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "structure": f"{enhanced_prompt} 摘要: "
                f"{'对以下小说文本进行结构设计分析' if prompt_template == 'simplified' else '重点对以下小说文本进行结构设计分析。章节安排、情节发展、高潮设置等'}"
                f"。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "outline_analysis": f"{enhanced_prompt} 摘要: 请对以下小说文本进行"
                f"{'大纲分析' if prompt_template == 'simplified' else '详细的大纲分析'}"
                f"。\n\n"
                f"{'特别注意：' if prompt_template == 'simplified' else '特别注意：'}\n"
                f"{'1. 整体故事大纲：概述故事内容，包括主要情节和人物发展\n2. 章节结构：分析章节内容和连贯性\n3. 整体结构：分析作品的结构设计\n4. 情节线设计：分析主线与支线关系\n5. 叙事节奏：分析高潮设计和节奏控制\n6. 人物系统：分析主要人物和配角特点\n7. 人物弧线：分析人物成长设计\n8. 主题展开：分析主题表达方式\n9. 世界观构建：分析背景设定' if prompt_template == 'simplified' else '1. 整体故事大纲与内容概述：全面详细地概述整部作品的故事内容，包括主要情节线、重要事件和人物发展。不要简单概括，而是要详细描述整个故事的发展过程，按照时间顺序或情节发展顺序进行叙述。确保覆盖所有关键情节点和转折点，让读者通过这部分内容就能完整了解整个故事讲述了什么。\n2. 章节结构与内容分布研究：详细分析作品的章节结构和内容分布。列出每个章节的主要内容和功能，评估章节之间的连贯性和平衡性。分析章节如何组织成更大的结构单元（如卷、部分或阶段），以及这些结构单元如何服务于整体叙事。\n3. 整体结构与架构设计评估：全面分析作品的整体结构与架构设计。评估三幕结构/五幕结构/英雄旅程等经典结构模式的应用情况。量化结构完整度和平衡性（开端15%/发展70%/结局15%）。分析结构创新点和变异处理。\n4. 情节线设计与管理考察：深入分析作品的情节线设计与管理。识别并详述主线与支线的数量、内容和层级关系。量化情节线的完整度和收束度。评估情节线交织技巧和节奏控制。分析各情节线如何服务于人物发展和主题表达。\n5. 叙事节奏与高潮设计研究：分析作品的叙事节奏与高潮设计。评估节奏变化的规律性和目的性。量化高潮分布密度和强度梯度。分析节奏控制技巧和高潮铺垫手法。评估节奏与高潮设计如何增强读者体验和情感投入。\n6. 人物系统与角色设计分析：全面分析作品的人物系统和角色设计。识别主要人物、次要人物和配角的功能和特点。评估人物形象的丰满度和一致性。分析人物关系网络的复杂性和动态变化。量化不同类型人物的分布比例。\n7. 人物弧线与成长设计评估：评估作品中人物弧线与成长设计。分析主要人物的起点、转折点和终点设置。量化人物变化的幅度和节奏。评估人物成长与情节发展的匹配度。分析人物弧线如何服务于主题表达和读者共鸣。\n8. 主题展开与深化策略研究：深入分析作品的主题展开与深化策略。识别核心主题和辅助主题的层级关系。量化主题元素的分布密度和强调程度。分析主题通过情节/人物/对话等不同载体的表达方式。评估主题展开的完整性和说服力。\n9. 世界观构建与背景设定考察：评估作品的世界观构建与背景设定。分析世界观元素的丰富度、一致性和原创性。量化世界观信息的揭示节奏和密度。评估世界观对故事氛围和读者沉浸感的影响。分析背景设定如何服务于情节发展和主题表达。'}\n\n"
                f"请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n"
                f"{'请生成小说大纲，帮助读者理解故事脉络' if prompt_template == 'simplified' else '请生成一个详细、全面的小说大纲，帮助读者理解整个故事的脉络和发展。内容不限字数，越详细越好。'}"
                f"\n\n文本：\n{text}",
            "sentence_variation": f"{enhanced_prompt} 摘要: "
                f"{'对以下小说文本进行句式结构分析' if prompt_template == 'simplified' else '重点对以下小说文本进行句式结构分析来创造节奏感和表达不同的情感'}"
                f"。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "paragraph_length": f"{enhanced_prompt} 摘要: "
                f"{'分析段落长度的变化及其影响' if prompt_template == 'simplified' else '分析段落长度的变化及其对阅读体验和情感表达的影响'}"
                f"。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "perspective_shifts": f"{enhanced_prompt} 摘要: "
                f"{'对以下小说文本进行视角转化分析' if prompt_template == 'simplified' else '重点对以下小说文本进行视角转化分析，包括叙事视角的变化及其效果'}"
                f"。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "paragraph_flow": f"{enhanced_prompt} 摘要: "
                f"{'对以下小说文本进行段落衔接分析' if prompt_template == 'simplified' else '重点对以下小说文本进行段落衔接术分析，分析段落之间的过渡和连接以及如何影响整体阅读流畅度'}"
                f"。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "novel_characteristics": f"{enhanced_prompt} 摘要: "
                f"{'分析小说的风格和写作技巧' if prompt_template == 'simplified' else '全面分析小说的风格、主题、写作技巧等特点'}"
                f"。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "world_building": f"{enhanced_prompt} 摘要: "
                f"{'分析小说世界构建方式' if prompt_template == 'simplified' else '分析作者如何构建小说世界，包括环境描写、背景设定等'}"
                f"。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "chapter_outline": f"{enhanced_prompt} 摘要: 请"
                f"{'分析小说文本，提取章节结构和内容大纲' if prompt_template == 'simplified' else '仔细分析以下小说文本，提取详细的章节结构和内容大纲'}"
                f"。\n\n"
                f"{'特别注意：' if prompt_template == 'simplified' else '特别注意：'}\n"
                f"{'1. 章节内容梳理：概述每章主要内容和情节发展\n2. 关键事件分析：识别章节中的转折点和高潮\n3. 章节结构：分析章节的内部结构和功能\n4. 人物表现：分析主要人物的行动和发展\n5. 章节连贯性：分析章节间的关系\n6. 主题基调：分析章节的主题和情感色彩\n7. 特色创新：识别章节中的独特元素\n8. 读者体验：分析章节如何吸引读者兴趣' if prompt_template == 'simplified' else '1. 章节内容详细梳理：全面详细地概述每个章节的主要内容，包括发生了什么事件、出现了哪些人物、情节如何发展。不要简单概括，而是要详细描述章节中的所有重要情节点，按照时间顺序或情节发展顺序进行叙述。确保覆盖章节中的所有关键场景和重要对话，让读者通过这部分内容就能完整了解章节讲述了什么故事。每个章节的内容描述至少应包含500字以上的详细叙述。\n2. 章节主要情节与关键事件：详细分析每个章节的主要情节线和关键事件。识别并详述章节中的转折点、冲突点和高潮点。分析每个关键事件的起因、经过和结果，以及对人物和整体故事的影响。评估这些事件的重要性和情感强度。每个关键事件的分析至少应包含300字以上的详细描述。\n3. 章节内部结构与功能定位：全面分析每个章节的内部结构和功能定位。评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性。分析章节的功能类型（推进型/铺垫型/转折型/高潮型/过渡型）及其在整体结构中的作用。\n4. 人物表现与发展分析：详细分析每个章节中主要人物的表现、行动和发展。描述每个重要人物在本章中做了什么、说了什么、想了什么，以及这些行为和思想如何反映人物性格和推动人物发展。分析人物之间的互动和关系变化。\n5. 章节间关系与叙事连贯性：深入分析章节之间的关系和叙事连贯性。评估章节过渡的自然度和技巧多样性。识别章节间的连接方式（直接延续/时空跳转/视角切换/平行叙事）及其效果。\n6. 章节主题与情感基调：评估每个章节的主题明确性和情感基调。分析章节的核心主题和情感色彩。量化章节主题的聚焦度和情感强度。评估章节主题与整体作品主题的呼应关系。\n7. 章节特色与创新点：分析每个章节的特色元素和创新点。识别章节中独特的写作技巧、情节设计或人物塑造方法。评估这些特色和创新对读者体验的影响和价值。\n8. 读者体验与期待管理：分析每个章节对读者体验和期待管理的设计。评估章节如何吸引和维持读者兴趣，如何管理读者期待。分析章末钩子的类型和强度。量化悬念的设置密度和解答比例。'}\n\n"
                f"请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n"
                f"{'请生成章节大纲，帮助读者理解章节内容和发展' if prompt_template == 'simplified' else '请生成一个详细、全面的章节大纲，帮助读者理解所有章节的内容和发展。内容不限字数，越详细越好。对于每个章节，至少提供500字以上的详细描述，确保覆盖所有重要情节点和人物发展。'}"
                f"\n\n文本：\n{text}",
            "character_relationships": f"""{enhanced_prompt} 摘要: "
                f"{'分析小说中的人物关系网络' if prompt_template == 'simplified' else '分析小说中的人物关系网络，包括主要角色之间的互动和发展'}"
                f"。

## {'人物关系分析指导' if prompt_template == 'simplified' else '特别重要的人物关系分析指导'}：
{'1. 使用通俗易懂的语言\n2. 避免晦涩难懂的术语\n3. 用简单直白的语言描述关系\n4. 使用具体的互动场景作例子\n5. 用日常比喻解释复杂关系\n6. 确保分析易于理解\n7. 避免晦涩表达\n8. 直接描述人物关系和变化' if prompt_template == 'simplified' else '1. 必须使用通俗易懂的日常语言，完全避免学术化、理论化的表达\n2. 禁止使用任何晦涩难懂的学术术语，如"身份花拓学"、"系统干预层"、"时空错位理论"等抽象概念\n3. 分析应该像是在与普通读者聊天，用简单直白的语言描述人物之间的关系\n4. 使用具体的人物互动场景作为例子，而不是抽象的理论框架\n5. 将复杂的关系用日常生活中常见的比喻来解释，如"像兄弟一样亲密"、"如同冤家路窄"等\n6. 分析结果应该是任何高中学历的读者都能轻松理解的内容\n7. 避免使用"红绿金铃构成权力关系的巴别塔系统"之类的晦涩表达\n8. 不要使用"认知性意识"、"系统干预"等抽象术语\n9. 不要创造不存在的学术理论或分析框架\n10. 分析应该直接描述"谁和谁是什么关系，他们之间发生了什么，关系如何变化"'}

请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。

{previous_analyses_summary if previous_analyses else ''}

文本：
{text}""",
            "opening_effectiveness": f"{enhanced_prompt} 摘要: "
                f"{'分析小说开篇效果' if prompt_template == 'simplified' else '分析小说开篇的效果，包括如何吸引读者注意力和设立基调'}"
                f"。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "climax_pacing": f"{enhanced_prompt} 摘要: "
                f"{'分析小说高潮情节设置' if prompt_template == 'simplified' else '分析小说中高潮情节的设置和节奏控制'}"
                f"。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "theme_analysis": f"{enhanced_prompt} 摘要: "
                f"{'分析小说的主题思想' if prompt_template == 'simplified' else '分析小说的主题思想，包括核心主题、辅助主题及其表达方式'}"
                f"。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "comprehensive_report": f"{enhanced_prompt} "
                f"{'请整合各维度分析，生成小说分析报告' if prompt_template == 'simplified' else '请整合以下各个维度的分析，生成一份全面的小说分析报告'}"
                f"。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n{text}",
            "popular_tropes": f"{enhanced_prompt} 摘要: "
                f"{'识别并统计文本中的热梗元素' if prompt_template == 'simplified' else '识别并统计文本中出现的热梗元素，包括网络流行梗、文化梗、类型文学常见梗等'}"
                f"。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "chapter_outline": f"""{enhanced_prompt} 摘要: "
                f"{'请分析小说文本，提取章节结构和内容大纲' if prompt_template == 'simplified' else '请仔细分析以下小说文本，提取详细的章节结构和内容大纲'}"
                f"。

## {'章纲分析要求' if prompt_template == 'simplified' else '章纲分析特别要求（必须首先执行）'}：
{'1. 基于原文：分析必须基于章节内容\n2. 详细叙述：描述章节中的场景和事件\n3. 不限字数：内容越详细越好\n4. 承接前章：展示章节间的连贯性\n5. 通俗易懂：使用清晰生动的语言' if prompt_template == 'simplified' else '1. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测\n2. 必须详细叙述原文内容：极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理活动\n3. 不限字数，越详细越好：内容重现部分必须极其详细，不设字数限制\n4. 必须承接上一章节的章纲分析：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展\n5. 语言必须通俗易懂：使用清晰、生动的语言，模仿原文的风格和语气'}

## {'重要指导' if prompt_template == 'simplified' else '极其重要的特别指导'}：
{'1. 主要内容部分要详细叙述\n2. 描述场景的环境细节和氛围\n3. 按时间顺序描述事件\n4. 记录主要人物的对话和表情\n5. 描述章节中的转折点\n6. 直接描述情节内容本身' if prompt_template == 'simplified' else '1. 【主要内容】部分必须详细叙述章节的主要内容，而且字数不少于1500字！\n2. 【主要内容】章节内容不设字数限制，越详细越好，最少不应少于1500字。绝对禁止简短概括或总结性描述。\n3. 必须详细描述每个场景的环境细节、氛围和背景，包括光线、声音、气味等感官细节，让读者仿佛身临其境。\n4. 必须按时间顺序详细描述每个事件的起因、经过和结果，不遗漏任何重要细节，包括具体动作和过程，就像你亲眼所见一样。\n5. 必须记录主要人物的对话内容和语气，保留原文中的重要对话，同时详细描述人物说话时的表情、动作和心理活动，让角色鲜活立体。\n6. 必须描述章节中的每个转折点和冲突，以及它们对人物和故事发展的影响。\n7. 【主要内容】的详细程度至关重要，它应该完整体验整个章节的内容和情感。\n8. 严格禁止在主要内容部分使用"该章讲述了..."、"本章描述了..."等总结性表达，必须直接描述情节内容本身。'}

{'特别分析要点：' if prompt_template == 'simplified' else '特别注意：'}
{'1. 章节内容梳理：叙述章节主要内容和情节发展\n2. 关键事件分析：识别章节中的转折点和高潮\n3. 章节结构：分析章节的内部结构和功能\n4. 人物表现：分析主要人物的行动和发展\n5. 章节连贯性：分析章节间的关系\n6. 主题基调：分析章节的主题和情感色彩\n7. 特色创新：识别章节中的独特元素\n8. 读者体验：分析章节如何吸引读者兴趣' if prompt_template == 'simplified' else '1. 章节内容详细梳理：全面详细地叙述每个章节的主要内容，包括发生了什么事件、出现了哪些人物、情节如何发展。不要简单概括，而是要详细描述章节中的所有重要情节点，按照时间顺序或情节发展顺序进行叙述。确保覆盖章节中的所有关键场景和重要对话，让读者通过这部分内容就能完整了解章节讲述了什么故事。每个章节的内容描述应该不设字数上限，尽可能详细生动。\n2. 章节主要情节与关键事件：详细分析每个章节的主要情节线和关键事件。识别并详述章节中的转折点、冲突点和高潮点。分析每个关键事件的起因、经过和结果，以及对人物和整体故事的影响。评估这些事件的重要性和情感强度。每个关键事件的分析至少应包含300字以上的详细描述。\n3. 章节内部结构与功能定位：全面分析每个章节的内部结构和功能定位。评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性。分析章节的功能类型（推进型/铺垫型/转折型/高潮型/过渡型）及其在整体结构中的作用。\n4. 人物表现与发展分析：详细分析每个章节中主要人物的表现、行动和发展。描述每个重要人物在本章中做了什么、说了什么、想了什么，以及这些行为和思想如何反映人物性格和推动人物发展。分析人物之间的互动和关系变化。\n5. 章节间关系与叙事连贯性：深入分析章节之间的关系和叙事连贯性。评估章节过渡的自然度和技巧多样性。识别章节间的连接方式（直接延续/时空跳转/视角切换/平行叙事）及其效果。\n6. 章节主题与情感基调：评估每个章节的主题明确性和情感基调。分析章节的核心主题和情感色彩。量化章节主题的聚焦度和情感强度。评估章节主题与整体作品主题的呼应关系。\n7. 章节特色与创新点：分析每个章节的特色元素和创新点。识别章节中独特的写作技巧、情节设计或人物塑造方法。评估这些特色和创新对读者体验的影响和价值。\n8. 读者体验与期待管理：分析每个章节对读者体验和期待管理的设计。评估章节如何吸引和维持读者兴趣，如何管理读者期待。分析章末钩子的类型和强度。量化悬念的设置密度和解答比例。'}

请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。

{previous_analyses_summary if previous_analyses else ''}

{'请生成章节大纲，帮助读者理解章节内容和发展' if prompt_template == 'simplified' else '请生成一个详细、全面的章节大纲，帮助读者理解所有章节的内容和发展。内容不限字数，越详细越好。对于每个章节，提供主要内容部分时，必须详细叙述章节的主要内容，不设字数上限，确保覆盖所有重要情节点和人物发展。记住，【主要内容】部分字数不少于1500字！'}

文本：
{text}""",
            "custom_analysis": text,  # 自定义分析直接使用传入的提示词
            "chapter_content_generation": text if text and text.strip() else "请生成章节内容",  # 章节内容生成，确保不为空
            "content_summary_generation": text if text and text.strip() else "请生成内容简介"  # 内容简介生成，确保不为空
        }

        # 默认使用通用提示词
        return prompts.get(analysis_type, f"请对以下小说文本进行{analysis_type}分析：\n\n{text}")

    def _get_sample_response(self, analysis_type: str, text: str) -> Dict[str, Any]:
        """
        Get a sample response for debugging purposes.

        Args:
            analysis_type: Type of analysis.
            text: Original text being analyzed.

        Returns:
            Sample response dictionary.
        """
        # 示例分析内容（根据不同分析类型返回不同的示例）
        first_para = text.split("\n")[0][:100] if text else "示例文本"

        # 特殊处理API测试连接
        if analysis_type == "test_connection":
            return {
                "type": "test_connection",
                "content": f"""# API连接测试成功

## 测试结果
✅ DeepSeek API连接测试成功！

## 测试详情
- **测试文本**: "{text}"
- **API模型**: {self.model}
- **API端点**: {self.endpoint}
- **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## API响应分析
这是一个API测试响应，用于验证DeepSeek API连接是否正常工作。API连接正常，可以进行小说分析。

## 系统状态
- 系统运行正常
- API连接正常
- 数据库连接正常
- 缓存系统正常

您可以放心使用九猫小说分析系统进行小说分析。"""
            }

        samples = {
            "language_style": f"# 语言风格分析\n\n## 概述\n\n分析的文本「{first_para}...」展现了独特的语言风格特点。\n\n## 详细分析\n\n### 词汇选择\n作者使用了丰富多样的词汇，包括...\n\n### 句式结构\n文本中的句式结构变化多样，短句和长句交替使用...\n\n### 修辞手法\n作者善于运用比喻、拟人等修辞手法，例如...\n\n### 语气语调\n整体语气平和但带有一定的抒情性，在描述...\n\n## 评价\n\n总体而言，该文本的语言风格独特鲜明，体现了作者扎实的语言功底和独特的艺术追求。",

            "rhythm_pacing": f"# 节奏与节奏分析\n\n## 整体节奏\n\n分析的文本「{first_para}...」在节奏控制上表现出明显的快慢变化。\n\n## 段落节奏\n段落长度的变化形成了独特的节奏感，短段落带来紧凑感，长段落则提供详细描写...\n\n## 情节推进\n故事情节的推进速度恰到好处，在关键情节点适当放慢，增强读者体验...\n\n## 高潮铺垫\n作者通过巧妙的节奏控制为高潮情节做了充分铺垫...\n\n## 总结\n文本在节奏把控上表现出作者成熟的写作技巧，既能吸引读者注意，又能保持故事的连贯性。",

            "structure": f"# 结构分析\n\n## 整体结构\n\n文本「{first_para}...」采用了传统的三段式结构，但在细节处理上有所创新。\n\n## 开端\n开篇迅速建立了故事背景和人物关系...\n\n## 发展\n中间部分通过三个关键事件推动情节发展...\n\n## 高潮与结局\n结尾部分既是情节高潮也是情感宣泄点，首尾呼应...\n\n## 章节安排\n章节划分合理，每个章节都有明确的功能...\n\n## 评价\n整体结构严谨而不失灵活，故事线索清晰，为读者提供了流畅的阅读体验。",

            "sentence_variation": f"# 句式变化分析\n\n## 整体特点\n\n文本「{first_para}...」的句式变化丰富多样，长短句搭配合理，复杂句和简单句交替使用，形成了良好的节奏感和表现力。\n\n## 句式类型分布\n- **简单句**：约占30%，主要用于表达直接、明确的信息和推动情节发展\n- **复合句**：约占40%，用于表达复杂的逻辑关系和丰富的情感层次\n- **长句**：约占20%，用于描绘场景和心理活动\n- **短句**：约占10%，用于强调关键信息和制造紧张氛围\n\n## 句式变化效果\n句式的多样化变化有效避免了语言的单调感，增强了文本的表现力和感染力。在情节紧张处，短句的运用增强了紧迫感；在抒情描写处，长句的运用增强了情感的表达。\n\n## 句式特色\n作者善于运用排比、设问、反问等修辞手法，增强了语言的表现力和感染力。同时，作者也注重句式的节奏感，通过句式长度的变化控制阅读节奏。\n\n## 总结评价\n作者对句式的灵活运用显示了成熟的写作技巧，句式变化为文本增添了丰富的表现力和感染力。",

            "paragraph_length": f"# 段落长度分析\n\n## 整体特点\n\n文本「{first_para}...」的段落长度呈现多样化特点，短段落与长段落交替使用，形成了良好的节奏感。\n\n## 段落长度分布\n- 短段落（1-3行）：约占30%，主要用于对话和关键转折点\n- 中等段落（4-8行）：约占50%，用于情节推进和场景描写\n- 长段落（9行以上）：约占20%，用于复杂心理描写和环境铺陈\n\n## 段落长度变化\n段落长度的变化与情节发展紧密相关，在情节紧张处使用短段落增强紧迫感，在情感抒发处使用长段落增强沉浸感。\n\n## 段落长度效果\n段落长度的变化有效控制了阅读节奏，避免了单一长度带来的阅读疲劳，增强了文本的可读性。\n\n## 总结评价\n作者对段落长度的控制显示了成熟的写作技巧，段落长度的变化为文本增添了节奏感和层次感。",

            "perspective_shifts": f"# 视角转换分析\n\n## 整体特点\n\n文本「{first_para}...」在叙事视角上展现了灵活多变的特点，主要采用了第三人称全知视角，但在关键情节处巧妙地转换为第三人称限制视角和第一人称视角。\n\n## 视角类型分布\n- **第三人称全知视角**：占主导地位，约占70%的篇幅，用于全面展示故事世界和多个人物的内心活动\n- **第三人称限制视角**：约占20%的篇幅，主要跟随主角视角，增强读者与主角的情感连接\n- **第一人称视角**：约占10%的篇幅，主要出现在回忆和内心独白部分，增强情感表达的真实性和直接性\n\n## 视角转换技巧\n作者在视角转换时注重自然过渡，通常通过场景切换、时间跳跃或情感变化作为转换契机，避免了生硬的转换。\n\n## 视角转换效果\n视角的灵活转换丰富了叙事层次，使读者能够从不同角度理解故事和人物，增强了故事的立体感和复杂性。\n\n## 总结评价\n作者对视角转换的熟练运用显示了高超的叙事技巧，视角转换为作品增添了丰富的表现力和艺术魅力。",

            "paragraph_flow": f"# 段落流畅度分析\n\n## 整体特点\n\n文本「{first_para}...」的段落之间衔接自然，过渡流畅，形成了连贯的叙事线索和阅读体验。\n\n## 段落衔接技巧\n- **逻辑衔接**：通过因果关系、时间顺序等逻辑关系连接段落，保持叙事的连贯性\n- **情感衔接**：通过情感的延续和变化连接段落，保持情感表达的连贯性\n- **意象衔接**：通过重复或变化的意象连接段落，增强文本的整体性\n- **关键词衔接**：通过关键词的重复或变化连接段落，强化主题和线索\n\n## 段落过渡效果\n段落之间的自然过渡使读者在阅读过程中感受不到明显的断裂，能够沉浸在故事世界中，跟随叙事和情感的发展。\n\n## 段落内部流畅度\n段落内部句子之间的衔接同样流畅，句式变化丰富但不突兀，形成了和谐的语言节奏。\n\n## 总结评价\n作者对段落流畅度的精心处理显示了成熟的写作技巧，流畅的段落过渡为文本增添了整体性和可读性。",

            "novel_characteristics": f"# 小说特点分析\n\n## 整体风格\n\n文本「{first_para}...」展现了独特的创作风格，融合了现实主义与浪漫主义的元素，形成了鲜明的个人特色。\n\n## 主题探索\n作品探讨了人性、爱情、成长等永恒主题，通过具体的人物命运展现了深刻的思考。\n\n## 叙事技巧\n采用了多视角叙事和非线性时间结构，增强了故事的复杂性和层次感。\n\n## 语言特色\n语言简洁而富有表现力，善于通过细节描写和意象塑造营造氛围。\n\n## 人物塑造\n人物形象丰满立体，性格发展合理，内心世界刻画细腻。\n\n## 总体评价\n这是一部具有较高文学价值的作品，在题材选择、结构安排和语言运用上都展现了作者的创新意识和艺术追求。",

            "world_building": f"# 世界构建分析\n\n## 整体特点\n\n文本「{first_para}...」在世界构建方面展现了丰富的想象力和细致的描绘，创造了一个既真实可信又富有特色的故事世界。\n\n## 环境描写\n作者通过细致的环境描写建立了鲜明的场景感，包括自然环境、城市风貌和室内空间等多个层面，使读者能够身临其境。\n\n## 社会背景\n作品构建了完整的社会背景，包括社会结构、文化习俗、历史背景等多个方面，为人物行为和故事发展提供了合理的社会环境。\n\n## 规则设定\n在故事世界中，作者建立了一套合理的规则系统，包括社会规范、人际关系准则等，使故事世界具有内在的逻辑性和一致性。\n\n## 细节处理\n作者注重细节描写，通过生活细节、环境细节和人物细节等多方面的描绘，增强了故事世界的真实感和立体感。\n\n## 总结评价\n作者在世界构建方面展现了出色的想象力和细致的描绘能力，创造了一个既真实可信又富有特色的故事世界，为故事的发展提供了坚实的基础。",

            "chapter_outline": f"# 章节大纲分析\n\n## 整体结构\n\n文本「{first_para}...」采用了三部曲结构，共分为序章、正文（12章）和尾声，结构完整，层次分明。\n\n## 章节划分\n- **序章**：引入背景和主要人物，设置悬念\n- **第一章至第四章**：铺陈基本情境，展开主要冲突\n- **第五章至第八章**：冲突升级，人物关系复杂化\n- **第九章至第十二章**：冲突解决，人物命运最终走向\n- **尾声**：总结全文，点明主题\n\n## 章节衔接\n章节之间衔接自然，前后呼应，通过伏笔和悬念维持读者兴趣。\n\n## 章节节奏\n章节长度适中，节奏把控得当，高潮与平缓部分交替出现。\n\n## 总体评价\n章节安排合理，结构严谨，有助于故事的顺畅展开和主题的深入表达。",

            "character_relationships": f"""# 人物关系分析

## 整体特点

文本「{first_para}...」中的人物关系非常生动有趣，就像我们现实生活中的人际关系一样，有友情、有矛盾、有爱恨情仇。

## 主要人物关系
- **主角和朋友们**：主角和朋友们之间的关系就像现实中的好朋友一样，互相支持、互相帮助。比如当主角遇到困难时，朋友们会伸出援手。
- **主角和对手**：主角和对手之间的关系就像体育比赛中的竞争对手，既有竞争又有尊重。他们因为目标相同而产生冲突，但也因为彼此的能力而相互欣赏。
- **家人之间**：故事中的家人关系非常真实，有欢笑也有争吵，有理解也有误会，就像我们自己的家庭一样。
- **恋爱关系**：故事中的恋爱关系描写得很真实，有甜蜜的时刻，也有吵架的时候，展现了真实的爱情。
- **师徒关系**：师傅和徒弟之间既有严厉的教导，也有深厚的关爱，就像良师益友的关系。

## 人物关系变化
故事中的人物关系并不是一成不变的，而是随着故事发展不断变化。比如，有些人一开始是敌人，后来成了朋友；有些人一开始关系很好，后来因为误会而疏远，最后又重归于好。这些变化让故事更加生动有趣。

## 人物关系网
整个故事中的人物关系就像一张网，每个人都和其他人有着这样那样的联系。这些关系互相影响，共同推动故事的发展。

## 总结
作者把人物关系描写得非常生动真实，让我们仿佛认识了这些人物，能够理解他们的喜怒哀乐，这也是这个故事吸引人的地方。""",

            "opening_effectiveness": f"# 开篇效果分析\n\n## 整体特点\n\n文本「{first_para}...」的开篇简洁有力，迅速吸引读者注意，并为后续故事发展奠定了坚实的基础。\n\n## 开篇技巧\n- **场景设置**：开篇通过简洁而生动的场景描写，迅速建立了故事的时空背景\n- **人物引入**：主要人物的引入自然而不突兀，通过行动和对话展现人物特点\n- **悬念设置**：开篇设置了恰到好处的悬念，激发读者的好奇心和阅读欲望\n- **基调确立**：开篇确立了作品的基调和氛围，为整个故事的风格定下了基调\n\n## 开篇功能\n开篇不仅吸引了读者注意，还为后续故事发展提供了必要的信息和背景，包括人物关系、故事背景和主要冲突等。\n\n## 开篇与整体的关系\n开篇与整体故事紧密相连，开篇中设置的伏笔和线索在后续故事中得到了合理的展开和解决。\n\n## 总结评价\n作者的开篇处理简洁有力，既吸引了读者注意，又为后续故事发展奠定了坚实的基础，展现了成熟的写作技巧。",

            "climax_pacing": f"# 高潮节奏分析\n\n## 高潮设置\n\n在文本「{first_para}...」中，作者精心设计了多个情节高潮，层层递进，最终达到故事的总高潮。\n\n## 铺垫技巧\n高潮前的铺垫充分而不拖沓，通过伏笔、暗示和情绪积累等多种手法，为高潮的到来做了充分准备。\n\n## 节奏控制\n在接近高潮时，作者通过加快情节节奏、缩短段落长度、增加对话比例等手法，增强了紧张感和急迫感。\n\n## 情感变化\n高潮部分的情感变化丰富而强烈，从紧张到释放，从恐惧到希望，从绝望到欣喜，情感的起伏增强了高潮的感染力。\n\n## 后续处理\n高潮过后，作者通过适当的情节缓冲和情感沉淀，使故事自然过渡到结局，避免了突兀的结束。\n\n## 总体评价\n作者对高潮节奏的把控精准到位，既满足了情节发展的需要，也照顾了读者的心理感受，展现了成熟的写作技巧。"
        }

        # 如果没有特定类型的示例，返回通用示例
        content = samples.get(analysis_type, f"# {analysis_type.replace('_', ' ').title()} 分析\n\n这是一个自动生成的示例分析结果，用于测试目的。\n\n分析的文本开头为「{first_para}...」\n\n## 主要特点\n\n1. 特点一\n2. 特点二\n3. 特点三\n\n## 详细分析\n\n此处应有详细分析内容。\n\n## 结论\n\n总体而言，这是一个高质量的文本，在{analysis_type.replace('_', ' ')}方面表现出色。")

        return {
            "type": analysis_type,
            "content": content
        }

    def _get_chunk_hash(self, text: str) -> str:
        """
        计算文本块的哈希值，用于缓存比较。

        Args:
            text: 文本内容

        Returns:
            文本的SHA-256哈希值
        """
        return hashlib.sha256(text.encode('utf-8')).hexdigest()

    def _get_or_create_intermediate_result(self, novel_id: int, chunk_index: int, chunk_text: str,
                                          analysis_type: str = None, force_refresh: bool = False) -> Optional[Dict[str, Any]]:
        """
        获取或创建中间分析结果，支持跨维度复用。

        Args:
            novel_id: 小说ID
            chunk_index: 块索引
            chunk_text: 块文本内容
            analysis_type: 分析类型
            force_refresh: 是否强制刷新缓存

        Returns:
            中间分析结果，如果没有找到或禁用了缓存，则返回None
        """
        if not config.CACHE_INTERMEDIATE_RESULTS:
            return None

        # 计算块内容的哈希值
        chunk_hash = self._get_chunk_hash(chunk_text)

        # 检查数据库中是否有缓存的中间结果
        session = Session()
        try:
            # 首先查找完全匹配的结果（相同小说、相同块索引、相同哈希值、相同维度）
            cached = session.query(IntermediateResult).filter_by(
                novel_id=novel_id,
                chunk_index=chunk_index,
                chunk_hash=chunk_hash,
                dimension=analysis_type
            ).first()

            # 如果没有找到完全匹配的结果，且允许跨维度复用，则查找基础分析结果
            if not cached and config.CACHE_REUSE_ACROSS_DIMENSIONS and analysis_type:
                cached = session.query(IntermediateResult).filter_by(
                    novel_id=novel_id,
                    chunk_index=chunk_index,
                    chunk_hash=chunk_hash,
                    dimension=None  # 基础分析结果没有特定维度
                ).first()

                if cached:
                    logger.info(f"找到可复用的基础分析结果: 小说ID={novel_id}, 块索引={chunk_index}")

            # 检查缓存是否有效（未过期且不强制刷新）
            if cached and not force_refresh:
                # 检查缓存是否过期
                cache_valid_days = config.INTERMEDIATE_CACHE_VALID_DAYS
                cache_expiry = cached.updated_at + timedelta(days=cache_valid_days)

                if datetime.now() < cache_expiry:
                    # 缓存有效，返回缓存结果
                    if cached.dimension == analysis_type:
                        # 维度特定的结果
                        if cached.dimension_result:
                            logger.info(f"使用缓存的维度特定结果: 小说ID={novel_id}, 块索引={chunk_index}, 维度={analysis_type}")
                            return {"type": analysis_type, "content": cached.dimension_result}

                    # 基础分析结果
                    if cached.result_data:
                        logger.info(f"使用缓存的基础分析结果: 小说ID={novel_id}, 块索引={chunk_index}")
                        return cached.result_data
                else:
                    logger.info(f"缓存已过期: 小说ID={novel_id}, 块索引={chunk_index}, 维度={cached.dimension}")

            return None
        finally:
            session.close()

    def _save_intermediate_result(self, novel_id: int, chunk_index: int, chunk_text: str,
                                 analysis_type: str, result: Dict[str, Any]) -> None:
        """
        保存中间分析结果到缓存。

        Args:
            novel_id: 小说ID
            chunk_index: 块索引
            chunk_text: 块文本内容
            analysis_type: 分析类型
            result: 分析结果
        """
        if not config.CACHE_INTERMEDIATE_RESULTS:
            return

        # 计算块内容的哈希值
        chunk_hash = self._get_chunk_hash(chunk_text)

        session = Session()
        try:
            # 查找现有缓存
            cached = session.query(IntermediateResult).filter_by(
                novel_id=novel_id,
                chunk_index=chunk_index,
                chunk_hash=chunk_hash,
                dimension=analysis_type
            ).first()

            # 提取结果内容
            content = result.get("content", "")
            if not isinstance(content, str):
                logger.warning(f"内容不是字符串类型: {type(content)}")
                try:
                    content = str(content)
                except Exception as e:
                    logger.error(f"转换内容为字符串时出错: {str(e)}")
                    content = "无法转换的内容"

            # 更新或创建缓存
            if cached:
                cached.dimension_result = content
                cached.updated_at = datetime.now()
                logger.info(f"更新中间结果缓存: 小说ID={novel_id}, 块索引={chunk_index}, 维度={analysis_type}")
            else:
                # 创建新的缓存记录
                try:
                    new_cache = IntermediateResult(
                        novel_id=novel_id,
                        chunk_index=chunk_index,
                        chunk_hash=chunk_hash,
                        dimension=analysis_type,
                        dimension_result=content,
                        result_data=None  # 维度特定的结果不包含基础分析数据
                    )
                    session.add(new_cache)
                    logger.info(f"创建新的中间结果缓存: 小说ID={novel_id}, 块索引={chunk_index}, 维度={analysis_type}")
                except Exception as e:
                    logger.error(f"创建中间结果缓存对象时出错: {str(e)}")
                    # 尝试更简单的方式创建缓存对象
                    try:
                        new_cache = IntermediateResult()
                        new_cache.novel_id = novel_id
                        new_cache.chunk_index = chunk_index
                        new_cache.chunk_hash = chunk_hash
                        new_cache.dimension = analysis_type
                        new_cache.dimension_result = content
                        new_cache.result_data = None
                        session.add(new_cache)
                        logger.info(f"使用备用方法创建中间结果缓存: 小说ID={novel_id}, 块索引={chunk_index}, 维度={analysis_type}")
                    except Exception as e2:
                        logger.error(f"使用备用方法创建中间结果缓存时出错: {str(e2)}")
                        raise

            session.commit()
        except Exception as e:
            logger.error(f"保存中间结果缓存时出错: {str(e)}")
            session.rollback()
        finally:
            session.close()

    def _split_text_into_chunks(self, text, analysis_type=None, max_chunk_size=None, overlap=200, prompt_template="default"):
        """
        将文本分割成合适大小的块，以便进行分析

        Args:
            text: 要分割的文本
            analysis_type: 分析类型，用于确定最大块大小
            max_chunk_size: 每个块的最大字符数，如果为None，则根据analysis_type从配置中获取
            overlap: 块之间的重叠字符数，以保持上下文连贯性
            prompt_template: 提示词模板类型，用于决定是否进行额外的文本压缩

        Returns:
            文本块列表
        """
        if not text:
            return []

        # 根据分析类型确定最大块大小
        if max_chunk_size is None:
            if analysis_type and hasattr(config, 'DIMENSION_CHUNK_SIZES'):
                max_chunk_size = config.DIMENSION_CHUNK_SIZES.get(analysis_type, config.DIMENSION_CHUNK_SIZES.get("default", 8000))
            else:
                max_chunk_size = getattr(config, 'MAX_CHUNK_SIZE', 8000)

        # 精简版提示词模式下，实施多种降本增效策略（只在精简版启用）
        if prompt_template == "simplified":
            # 策略1：优化文本分块策略（新降本增效方案6）
            original_max_chunk_size = max_chunk_size
            original_overlap = overlap

            # 增加块大小，减少API调用次数
            max_chunk_size = int(max_chunk_size * 1.8)  # 增加80%的块大小，更激进的优化
            # 减少重叠，进一步减少总token数量
            overlap = max(50, int(overlap * 0.3))  # 重叠减少70%，更激进的优化

            logger.info(f"[新降本增效] 精简版模式：块大小从{original_max_chunk_size}增加80%至{max_chunk_size}，重叠从{original_overlap}减少70%至{overlap}")

            # 策略2：智能文本压缩（新降本增效方案3）
            original_length = len(text)
            text = self._compress_text_intelligently(text, analysis_type)
            compressed_length = len(text)
            compression_ratio = (original_length - compressed_length) / original_length * 100
            logger.info(f"[新降本增效] 智能文本压缩：原长度{original_length}字符，压缩后{compressed_length}字符，压缩率{compression_ratio:.1f}%")

            # 策略3：动态块大小调整（新降本增效方案7）
            if compressed_length > 50000:  # 对于大文本，进一步增加块大小
                max_chunk_size = int(max_chunk_size * 1.2)
                logger.info(f"[新降本增效] 大文本检测：进一步增加块大小至{max_chunk_size}，减少API调用次数")
            elif compressed_length < 10000:  # 对于小文本，适度减少块大小以保证质量
                max_chunk_size = int(max_chunk_size * 0.8)
                logger.info(f"[新降本增效] 小文本检测：适度减少块大小至{max_chunk_size}，保证分析质量")

        logger.info(f"使用最大块大小: {max_chunk_size} 字符，重叠: {overlap} 字符")

        # 如果文本长度小于最大块大小，直接返回整个文本
        if len(text) <= max_chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            # 计算当前块的结束位置
            end = min(start + max_chunk_size, len(text))

            # 如果不是最后一个块，尝试在适当的位置（句号、问号、感叹号等）切割
            if end < len(text):
                # 尝试在句子边界切割
                sentence_boundaries = ['.', '!', '?', '。', '！', '？', '\n\n']
                for i in range(end - 1, max(start + max_chunk_size // 2, start), -1):
                    if text[i] in sentence_boundaries:
                        end = i + 1  # 包含句子结束符
                        break

            # 添加当前块
            chunks.append(text[start:end])

            # 计算下一个块的起始位置，考虑重叠
            start = max(0, end - overlap)

        return chunks

    def _combine_chunk_results(self, chunk_results, dimension):
        """
        组合多个文本块的分析结果

        Args:
            chunk_results: 每个文本块的分析结果列表
            dimension: 分析维度

        Returns:
            合并后的分析结果
        """
        # 如果只有一个块，直接返回该块的结果
        if len(chunk_results) == 1:
            return chunk_results[0]

        # 提取每个块的内容
        contents = []
        for result in chunk_results:
            if result and "content" in result:
                contents.append(result["content"])
            else:
                logger.warning(f"块分析结果格式不正确，跳过: {result}")

        if not contents:
            logger.error("没有有效的块分析结果内容，无法合并")
            return {"content": "分析失败：没有有效的块分析结果"}

        # 专门处理章节大纲维度
        if dimension == "chapter_outline":
            return self._combine_chapter_outline_results(contents)

        # 使用_generate_combined_content方法组合内容
        combined_content = self._generate_combined_content(contents, dimension)

        return {
            "type": dimension,
            "content": combined_content
        }

    def _generate_combined_content(self, contents, analysis_type):
        """
        生成组合内容

        Args:
            contents: 内容列表
            analysis_type: 分析类型

        Returns:
            组合后的内容
        """
        # 简单组合所有内容
        combined = f"# {analysis_type.replace('_', ' ').title()} 分析\n\n"

        # 提取每个块的主要部分
        for i, content in enumerate(contents):
            combined += f"## 文本块 {i+1} 分析\n\n"

            # 提取主要内容（最多2000字符）
            content_preview = content[:2000] if len(content) > 2000 else content
            combined += content_preview + "\n\n"

        combined += "## 总结\n\n"
        combined += "综合以上分析，本小说在" + analysis_type.replace('_', ' ') + "方面表现出以下特点...\n\n"

        # 对于非DEBUG模式，使用API进行一次整合分析
        try:
            # 创建整合提示
            integration_prompt = f"你是文学分析专家，请将以下关于{analysis_type.replace('_', ' ')}的分析结果整合为一个连贯的整体分析报告：\n\n"

            for i, content in enumerate(contents):
                integration_prompt += f"### 片段 {i+1} 分析:\n{content[:800]}...\n\n"  # 限制长度

            # 调用API进行整合
            integration_result = self.analyze_text(integration_prompt, f"integrate_{analysis_type}")

            if "error" not in integration_result:
                return integration_result.get("content", combined)  # 如果成功，返回整合结果

        except Exception as e:
            logger.error(f"整合分析结果时出错: {str(e)}")

        # 如果整合失败，返回简单组合的内容
        return combined

    def _combine_chapter_outline_results(self, contents):
        """
        专门用于组合章节大纲分析的结果

        Args:
            contents: 章节大纲分析内容列表

        Returns:
            合并后的章节大纲分析结果
        """
        # 创建合并章节大纲的提示词
        combined_prompt = """请整合以下多个文本块的章节大纲分析结果，生成一个完整连贯的章节大纲分析：

        ## 重要指导：
        1. 识别并合并相同章节的分析结果，避免重复
        2. 对于被分割在不同文本块中的章节，将其内容完整地整合在一起
        3. 确保章节的顺序正确，按照章节编号或顺序排列
        4. 对于每个章节，提供详细的内容叙述、关键情节点和重要人物
        5. 分析章节之间的连贯性和情节发展

        以下是多个文本块的章节大纲分析结果：
        """

        # 添加每个块的分析结果，提供更多上下文
        for i, content in enumerate(contents):
            # 提取更多内容，确保主要内容部分能够完整保留
            content_preview = content[:8000] if len(content) > 8000 else content
            combined_prompt += f"\n\n## 文本块 {i+1} 分析结果:\n{content_preview}"

        # 添加输出格式指导
        combined_prompt += """

        请按照以下格式整合上述分析结果：

        # 章节大纲分析

        ## 整体结构
        [提供小说的整体结构分析，包括章节数量、结构特点、主要情节线索等]

        ## 详细章节大纲
        [按顺序列出每个章节的详细内容，格式如下]

        ### 第X章：[章节标题]
        - **主要内容**：[详细描述本章的内容，包含场景描述、事件过程、人物对话和心理活动，以及情节转折和冲突]
        - **关键情节点**：[列出本章的关键情节发展]
        - **重要人物**：[列出本章出现的重要人物及其行动]
        - **与整体故事的关系**：[分析本章在整体故事中的作用和地位]

        ## 章节间关系与发展
        [分析章节之间的连贯性、情节发展和主题推进]

        ## 总体评价
        [对小说章节结构的整体评价]
        """

        # 使用更大的max_tokens，确保能够生成完整的合并结果
        combined_result = self.analyze_text(combined_prompt, "combine_chapter_outline", max_tokens=10000)

        logger.info(f"章节大纲合并完成，结果长度: {len(combined_result.get('content', ''))}")

        return {
            "type": "chapter_outline",
            "content": combined_result.get("content", "无法合并章节大纲分析结果")
        }

    def analyze_novel_in_chunks(self, novel_text: str, analysis_type: str, prompt_template: str = "default", temperature: float = None) -> Dict[str, Any]:
        """
        Analyze a novel by breaking it into chunks and then combining the results.
        Uses caching to optimize API calls and improve performance.

        Args:
            novel_text: The complete novel text.
            analysis_type: Type of analysis to perform.
            prompt_template: Prompt template to use (default or simplified).
            temperature: Optional temperature parameter for controlling randomness.

        Returns:
            Combined analysis result.
        """
        # 设置当前分析类型
        self.current_analysis_type = analysis_type
        novel_id = getattr(self, 'current_novel_id', None)

        logger.info(f"开始对小说进行{analysis_type}分析，总长度: {len(novel_text)} 字符")

        # 记录API调用统计信息
        try:
            from src.web.app import add_analysis_log
            if novel_id:
                # 获取当前API调用统计
                stats = self.get_api_call_stats()
                add_analysis_log(novel_id, f"[API统计] 开始分析前: 已调用{stats['total_calls']}次API, 累计费用: {stats['total_cost']:.4f}元", "info", analysis_type)
                # 预估费用（假设每1000字符约需要2.5个令牌）
                estimated_tokens = len(novel_text) * 2.5 / 1000
                estimated_input_cost = estimated_tokens * config.API_COST_INPUT_PER_1K_TOKENS
                estimated_output_cost = estimated_tokens * 0.3 * config.API_COST_OUTPUT_PER_1K_TOKENS  # 假设输出是输入的30%
                estimated_total_cost = estimated_input_cost + estimated_output_cost
                add_analysis_log(novel_id, f"[API费用] 预估本次分析可能消耗约 {estimated_tokens:.0f}K 令牌，预计费用约 {estimated_total_cost:.2f} 元", "info", analysis_type)
        except Exception as log_error:
            logger.error(f"添加API统计日志时出错: {str(log_error)}")

        # 分割文本为块，传递分析类型以优化分块大小
        chunks = self._split_text_into_chunks(novel_text, analysis_type, prompt_template=prompt_template)
        logger.info(f"小说已分割为 {len(chunks)} 个块，使用 {analysis_type} 分析的优化分块策略，提示词模板: {prompt_template}")

        # 分析每个块，使用缓存优化API调用
        chunk_results = []
        for i, chunk in enumerate(chunks):
            logger.info(f"分析第 {i+1}/{len(chunks)} 块，长度: {len(chunk)} 字符")

            # 检查是否有缓存的中间结果
            cached_result = None
            if novel_id and config.CACHE_INTERMEDIATE_RESULTS:
                cached_result = self._get_or_create_intermediate_result(
                    novel_id=novel_id,
                    chunk_index=i,
                    chunk_text=chunk,
                    analysis_type=analysis_type,
                    force_refresh=config.FORCE_REFRESH_CACHE
                )

            if cached_result:
                # 使用缓存结果
                logger.info(f"使用缓存的分析结果: 块 {i+1}/{len(chunks)}")
                if novel_id:
                    try:
                        from src.web.app import add_analysis_log
                        add_analysis_log(novel_id, f"[缓存] 使用缓存的分析结果: 块 {i+1}/{len(chunks)}", "info", analysis_type)
                    except Exception as log_error:
                        logger.error(f"添加缓存日志时出错: {str(log_error)}")
                chunk_results.append(cached_result)
            else:
                # 调用API进行分析，使用动态max_tokens
                # 对于整本书分析，自动启用流式输出以处理大量内容
                use_stream = len(novel_text) > 50000 or analysis_type in ["chapter_outline", "outline_analysis"]
                if use_stream:
                    logger.info(f"[流式输出] 检测到大量内容分析，自动启用流式输出模式")

                result = self.analyze_text(
                    chunk,
                    analysis_type,
                    prompt_template=prompt_template,
                    temperature=temperature,
                    stream=use_stream
                )
                chunk_results.append(result)

                # 保存结果到缓存
                if novel_id and config.CACHE_INTERMEDIATE_RESULTS and "error" not in result:
                    self._save_intermediate_result(
                        novel_id=novel_id,
                        chunk_index=i,
                        chunk_text=chunk,
                        analysis_type=analysis_type,
                        result=result
                    )

        # 结合所有块的分析结果
        if len(chunk_results) == 1:
            return chunk_results[0]
        else:
            return self._combine_chunk_results(chunk_results, analysis_type)

    def _handle_stream_response(self, response, analysis_type: str, progress_callback=None) -> Dict[str, Any]:
        """
        处理流式输出响应 - 特别适用于整本书分析

        Args:
            response: 流式响应对象
            analysis_type: 分析类型
            progress_callback: 进度回调函数

        Returns:
            分析结果字典
        """
        try:
            logger.info(f"[流式输出] 开始处理流式响应，分析类型: {analysis_type}")

            # 初始化流式输出变量
            full_content = ""
            reasoning_content = ""
            is_reasoning_phase = True  # DeepSeek R1 先输出思考过程，再输出最终答案
            chunk_count = 0
            total_tokens = 0

            # 流式处理响应
            for line in response.iter_lines(decode_unicode=True):
                if not line:
                    continue

                chunk_count += 1

                # 处理Server-Sent Events格式
                if line.startswith('data: '):
                    data_str = line[6:]  # 移除'data: '前缀

                    # 检查是否是结束标记
                    if data_str.strip() == '[DONE]':
                        logger.info(f"[流式输出] 接收到结束标记，总共处理 {chunk_count} 个数据块")
                        break

                    try:
                        # 解析JSON数据
                        chunk_data = json.loads(data_str)

                        # 提取内容
                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                            choice = chunk_data['choices'][0]
                            delta = choice.get('delta', {})

                            # 处理思考过程内容
                            if 'reasoning_content' in delta and delta['reasoning_content']:
                                reasoning_chunk = delta['reasoning_content']
                                reasoning_content += reasoning_chunk

                                # 调用进度回调
                                if progress_callback:
                                    progress_callback({
                                        'type': 'reasoning',
                                        'content': reasoning_chunk,
                                        'total_reasoning': len(reasoning_content),
                                        'chunk_count': chunk_count
                                    })

                                logger.debug(f"[流式输出] 思考过程块 {chunk_count}: {len(reasoning_chunk)} 字符")

                            # 处理最终回答内容
                            elif 'content' in delta and delta['content']:
                                content_chunk = delta['content']
                                full_content += content_chunk
                                is_reasoning_phase = False

                                # 调用进度回调
                                if progress_callback:
                                    progress_callback({
                                        'type': 'content',
                                        'content': content_chunk,
                                        'total_content': len(full_content),
                                        'chunk_count': chunk_count
                                    })

                                logger.debug(f"[流式输出] 内容块 {chunk_count}: {len(content_chunk)} 字符")

                        # 提取token使用信息
                        if 'usage' in chunk_data:
                            usage = chunk_data['usage']
                            total_tokens = usage.get('total_tokens', 0)

                    except json.JSONDecodeError as e:
                        logger.warning(f"[流式输出] 解析JSON数据块失败: {e}, 数据: {data_str[:100]}...")
                        continue

                # 每100个块输出一次进度
                if chunk_count % 100 == 0:
                    logger.info(f"[流式输出] 已处理 {chunk_count} 个数据块，思考过程: {len(reasoning_content)} 字符，内容: {len(full_content)} 字符")

            # 流式输出完成
            logger.info(f"[流式输出] 流式处理完成")
            logger.info(f"[流式输出] 总数据块: {chunk_count}")
            logger.info(f"[流式输出] 思考过程长度: {len(reasoning_content)} 字符")
            logger.info(f"[流式输出] 最终内容长度: {len(full_content)} 字符")
            logger.info(f"[流式输出] 总token数: {total_tokens}")

            # 验证内容质量
            if not full_content.strip():
                logger.error(f"[流式输出] 未接收到有效内容")
                return {
                    "error": "流式输出未接收到有效内容",
                    "type": analysis_type,
                    "reasoning_content": reasoning_content
                }

            # 构建返回结果
            result = {
                "type": analysis_type,
                "content": full_content.strip(),
                "reasoning_content": reasoning_content.strip() if reasoning_content else "",
                "stream_info": {
                    "chunk_count": chunk_count,
                    "total_tokens": total_tokens,
                    "reasoning_length": len(reasoning_content),
                    "content_length": len(full_content)
                }
            }

            logger.info(f"[流式输出] 成功完成 {analysis_type} 流式分析")
            return result

        except Exception as e:
            logger.error(f"[流式输出] 处理流式响应时出错: {str(e)}")
            return {
                "error": f"流式输出处理失败: {str(e)}",
                "type": analysis_type
            }
