# 临时文件目录 - 由optimize_nine_cats.py添加
TEMP_DIR = "E:\\艹，又来一次\\九猫\\nine_cats_temp"
CACHE_DIR = "E:\\艹，又来一次\\九猫\\nine_cats_temp\\cache"
LOG_DIR = "E:\\艹，又来一次\\九猫\\nine_cats_temp\\logs"
ANALYSIS_TEMP_DIR = "E:\\艹，又来一次\\九猫\\nine_cats_temp\\analysis"

# 内存优化设置 - 由optimize_nine_cats.py添加
CHUNK_SIZE = 512 * 1024  # 512KB，降低内存占用
MAX_CACHE_SIZE = 50 * 1024 * 1024  # 50MB，降低内存占用
ENABLE_MEMORY_OPTIMIZATION = True

# 短篇小说优化设置 - 避免内存累积问题
SHORT_STORY_MAX_LENGTH = 15000  # 定义短篇小说最大字符数
SHORT_STORY_CHUNK_SIZE = 3000   # 短篇小说的分块大小
SHORT_STORY_MAX_PARALLEL = 2    # 短篇小说的最大并行分析数
SHORT_STORY_FORCE_GC = True     # 短篇小说分析时强制垃圾回收
SHORT_STORY_PROGRESSIVE_LOADING = True  # 短篇小说启用渐进式加载

"""
Configuration settings for the 九猫 (Nine Cats) novel analysis system.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# 分块大小设置
MAX_CHUNK_SIZE = int(os.getenv("MAX_CHUNK_SIZE", "10000"))  # 最大分块大小

# 阿里云API配置
# 支持多个模型的API密钥配置

# DeepSeek R1 API配置（阿里云）
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY", "***********************************")
if deepseek_api_key.startswith("Bearer "):
    deepseek_api_key = deepseek_api_key[7:]  # 移除Bearer前缀
DEEPSEEK_API_KEY = deepseek_api_key
# 确保使用最新的API密钥
print(f"使用DeepSeek R1 API密钥（阿里云）: {DEEPSEEK_API_KEY[:6]}...")

# DeepSeek R1-0528 官方API配置
deepseek_official_api_key = os.getenv("DEEPSEEK_OFFICIAL_API_KEY", "***********************************")
if deepseek_official_api_key.startswith("Bearer "):
    deepseek_official_api_key = deepseek_official_api_key[7:]  # 移除Bearer前缀
DEEPSEEK_OFFICIAL_API_KEY = deepseek_official_api_key
# 确保使用最新的API密钥
print(f"使用DeepSeek R1-0528 官方API密钥: {DEEPSEEK_OFFICIAL_API_KEY[:6]}...")

# 通义千问-Plus-Latest API配置
qwen_api_key = os.getenv("QWEN_API_KEY", "sk-6f3b4c6ad9f64f78b22bed422c5d278d")
if qwen_api_key.startswith("Bearer "):
    qwen_api_key = qwen_api_key[7:]  # 移除Bearer前缀
QWEN_API_KEY = qwen_api_key

# 通义千问-QVQ-Max API配置
qwen_qvq_api_key = os.getenv("QWEN_QVQ_API_KEY", "sk-461690d08ac64555a16e195aff80edd4")
if qwen_qvq_api_key.startswith("Bearer "):
    qwen_qvq_api_key = qwen_qvq_api_key[7:]  # 移除Bearer前缀
QWEN_QVQ_API_KEY = qwen_qvq_api_key

# 确保API基础URL正确 - 阿里云API
DASHSCOPE_API_BASE_URL = os.getenv("DASHSCOPE_API_BASE_URL", "https://dashscope.aliyuncs.com/api/v1")

# DeepSeek 官方API基础URL
DEEPSEEK_OFFICIAL_API_BASE_URL = os.getenv("DEEPSEEK_OFFICIAL_API_BASE_URL", "https://api.deepseek.com")

# 默认模型配置
DEFAULT_MODEL = os.getenv("DEFAULT_MODEL", "deepseek-r1-0528-official")  # 🔧 修改：默认使用DeepSeek R1-0528官方模型

# 支持的模型列表
SUPPORTED_MODELS = {
    "deepseek-r1": {
        "name": "DeepSeek R1 (阿里云)",
        "api_key": DEEPSEEK_API_KEY,
        "endpoint": f"{DASHSCOPE_API_BASE_URL}/services/aigc/text-generation/generation",
        "max_tokens": 12000,
        "description": "阿里云DeepSeek R1模型，适合长文本分析",
        "provider": "aliyun"
    },
    "deepseek-r1-0528-official": {
        "name": "DeepSeek R1-0528 (官方)",
        "api_key": DEEPSEEK_OFFICIAL_API_KEY,
        "endpoint": f"{DEEPSEEK_OFFICIAL_API_BASE_URL}/v1/chat/completions",
        "max_tokens": 128000,  # 支持最高128K输出
        "description": "DeepSeek官方R1-0528模型，支持深度思考模式",
        "provider": "deepseek_official",
        "model_name": "deepseek-reasoner",  # 🔧 修正：根据官方文档，deepseek-reasoner指向DeepSeek-R1-0528
        "supports_reasoning": True  # 支持深度思考模式
    },
    "qwen-plus-latest": {
        "name": "通义千问-Plus-Latest",
        "api_key": QWEN_API_KEY,
        "endpoint": f"{DASHSCOPE_API_BASE_URL}/services/aigc/text-generation/generation",
        "max_tokens": 12000,
        "description": "阿里云通义千问-Plus-Latest模型，适合中文文本分析"
    },
    "qwen-qvq-max": {
        "name": "通义千问-QVQ-Max",
        "api_key": QWEN_QVQ_API_KEY,
        "endpoint": f"{DASHSCOPE_API_BASE_URL}/services/aigc/text-generation/generation",
        "max_tokens": 8192,  # 根据API错误信息，max_tokens不能超过8192
        "description": "阿里云通义千问-QVQ-Max模型，适合高质量中文文本生成"
    }
}

# API调用频率限制设置
API_CALL_LIMIT_PER_HOUR = int(os.getenv("API_CALL_LIMIT_PER_HOUR", "100"))  # 每小时API调用限制
API_CALL_WINDOW_SECONDS = int(os.getenv("API_CALL_WINDOW_SECONDS", "3600"))  # API调用时间窗口（秒）
API_RATE_LIMIT_ENABLED = os.getenv("API_RATE_LIMIT_ENABLED", "True").lower() in ("true", "1", "t")  # 是否启用API调用频率限制

# API费用设置 - 阿里云DeepSeek R1 API计费规则
API_COST_INPUT_PER_1K_TOKENS = float(os.getenv("API_COST_INPUT_PER_1K_TOKENS", "0.004"))  # 输入：每千tokens约0.004元
API_COST_OUTPUT_PER_1K_TOKENS = float(os.getenv("API_COST_OUTPUT_PER_1K_TOKENS", "0.016"))  # 输出：每千tokens约0.016元

# Application settings
# 开启DEBUG模式，以便查看更多日志信息
# DEBUG = False  # 注释掉硬编码 DEBUG，使用环境变量控制模式
DEBUG = False  # 强制设置为False，以确保真实调用API而不是模拟分析
FORCE_REANALYSIS = True  # 强制重新分析，不使用缓存
FORCE_REAL_API = True  # 强制使用真实API调用，即使在DEBUG模式下
SECRET_KEY = os.getenv("SECRET_KEY", "dev-key-change-in-production")
PORT = int(os.getenv("PORT", "5001"))
HOST = os.getenv("HOST", "0.0.0.0")

# Database settings
DATABASE_URI = os.getenv("DATABASE_URI", "sqlite:///novels.db")

# Analysis settings
OVERLAP_SIZE = int(os.getenv("OVERLAP_SIZE", "100"))  # Overlap between chunks for context (reduced from 200)
MAX_NOVEL_SIZE = int(os.getenv("MAX_NOVEL_SIZE", "3000000"))  # Maximum novel size in characters
MAX_PARALLEL_ANALYSES = int(os.getenv("MAX_PARALLEL_ANALYSES", "3"))  # Maximum number of parallel analyses (one per dimension) - 降低并行度
MAX_CHUNK_WORKERS = int(os.getenv("MAX_CHUNK_WORKERS", "3"))  # Maximum number of parallel workers per dimension - 降低并行度
PARALLEL_ANALYSIS_ENABLED = os.getenv("PARALLEL_ANALYSIS_ENABLED", "True").lower() in ("true", "1", "t")  # 是否启用并行分析

# 不同维度的分块大小配置 - 优化API调用效率 (降低以节省资源)
DIMENSION_CHUNK_SIZES = {
    "language_style": 8000,  # 语言风格分析
    "rhythm_pacing": 8000,  # 节奏分析
    "structure": 8000,  # 结构分析
    "sentence_variation": 6000,  # 句式变化分析
    "paragraph_length": 6000,  # 段落长度分析
    "perspective_shifts": 6000,  # 视角转换分析
    "paragraph_flow": 6000,  # 段落流畅度分析
    "novel_characteristics": 8000,  # 小说特点分析
    "world_building": 8000,  # 世界构建分析
    "chapter_outline": 8000,  # 章节大纲分析
    "character_relationships": 8000,  # 人物关系分析
    "opening_effectiveness": 6000,  # 开篇效果分析
    "climax_pacing": 6000,  # 高潮节奏分析
    "outline_analysis": 8000,  # 大纲分析
    "popular_tropes": 8000,  # 热梗统计
    "combine_chapter_outline": 8000,  # 合并章节大纲分析
    "combine_character_relationships": 8000,  # 合并人物关系分析
    "comprehensive_report": 8000,  # 综合报告
    "default": 6000  # 默认值
}

# 短篇小说特殊分块大小配置 - 显著降低避免内存累积问题
SHORT_STORY_DIMENSION_CHUNK_SIZES = {
    "language_style": 4000,
    "rhythm_pacing": 4000,
    "structure": 4000,
    "sentence_variation": 3000,
    "paragraph_length": 3000,
    "perspective_shifts": 3000,
    "paragraph_flow": 3000,
    "novel_characteristics": 4000,
    "world_building": 4000,
    "chapter_outline": 4000,
    "character_relationships": 4000,
    "opening_effectiveness": 3000,
    "climax_pacing": 3000,
    "outline_analysis": 4000,
    "popular_tropes": 4000,
    "combine_chapter_outline": 4000,
    "combine_character_relationships": 4000,
    "comprehensive_report": 4000,
    "default": 3000
}

# 不同维度的最大输出token配置 (降低以减少内存占用)
DIMENSION_MAX_TOKENS = {
    "language_style": 10000,
    "rhythm_pacing": 10000,
    "structure": 12000,
    "sentence_variation": 8000,
    "paragraph_length": 8000,
    "perspective_shifts": 8000,
    "paragraph_flow": 8000,
    "novel_characteristics": 10000,
    "world_building": 10000,
    "chapter_outline": 12000,
    "character_relationships": 10000,
    "opening_effectiveness": 8000,
    "climax_pacing": 8000,
    "outline_analysis": 12000,
    "popular_tropes": 10000,
    "combine_chapter_outline": 12000,
    "combine_character_relationships": 12000,
    "comprehensive_report": 12000,
    "default": 8000
}

# 短篇小说特殊token配置 - 降低以避免内存累积
SHORT_STORY_DIMENSION_MAX_TOKENS = {
    "language_style": 8000,
    "rhythm_pacing": 8000,
    "structure": 8000,
    "sentence_variation": 6000,
    "paragraph_length": 6000,
    "perspective_shifts": 6000,
    "paragraph_flow": 6000,
    "novel_characteristics": 8000,
    "world_building": 8000,
    "chapter_outline": 8000,
    "character_relationships": 8000,
    "opening_effectiveness": 6000,
    "climax_pacing": 6000,
    "outline_analysis": 8000,
    "popular_tropes": 8000,
    "combine_chapter_outline": 8000,
    "combine_character_relationships": 8000,
    "comprehensive_report": 8000,
    "default": 6000
}

# 缓存设置
CACHE_ENABLED = True  # 启用缓存，减少API调用
CACHE_VALID_DAYS = int(os.getenv("CACHE_VALID_DAYS", "7"))  # 缓存有效期（天）
FORCE_REFRESH_CACHE = False  # 不强制刷新缓存
FORCE_REANALYSIS = False  # 不强制重新分析，使用缓存的结果

# 高级缓存设置 - 优化API调用效率
CACHE_INTERMEDIATE_RESULTS = True  # 启用中间分析结果缓存
CACHE_REUSE_ACROSS_DIMENSIONS = True  # 允许跨维度复用结果
INTERMEDIATE_CACHE_VALID_DAYS = int(os.getenv("INTERMEDIATE_CACHE_VALID_DAYS", "3"))  # 中间结果缓存有效期（天）

# 短篇小说内存管理优化
MEMORY_CLEANUP_AFTER_EACH_STEP = True  # 每步后清理内存
INCREMENTAL_GARBAGE_COLLECTION = True  # 增量垃圾回收
AGGRESSIVE_MEMORY_MANAGEMENT = True    # 激进的内存管理

# Analysis dimensions
# 所有维度都已启用
DISABLED_DIMENSIONS = []

# 启用所有分析维度
ANALYSIS_DIMENSIONS = [
    {"key": "language_style", "name": "语言风格", "icon": "fas fa-language"},
    {"key": "rhythm_pacing", "name": "节奏节拍", "icon": "fas fa-drum"},
    {"key": "structure", "name": "结构分析", "icon": "fas fa-sitemap"},
    {"key": "sentence_variation", "name": "句式变化", "icon": "fas fa-text-width"},
    {"key": "paragraph_length", "name": "段落长度", "icon": "fas fa-indent"},
    {"key": "perspective_shifts", "name": "视角转换", "icon": "fas fa-eye"},
    {"key": "paragraph_flow", "name": "段落流畅度", "icon": "fas fa-stream"},
    {"key": "novel_characteristics", "name": "小说特点", "icon": "fas fa-fingerprint"},
    {"key": "world_building", "name": "世界构建", "icon": "fas fa-globe"},
    {"key": "character_relationships", "name": "人物关系", "icon": "fas fa-users"},
    {"key": "opening_effectiveness", "name": "开篇效果", "icon": "fas fa-door-open"},
    {"key": "climax_pacing", "name": "高潮节奏", "icon": "fas fa-mountain"},
    {"key": "chapter_outline", "name": "章纲分析", "icon": "fas fa-list-ol"},
    {"key": "outline_analysis", "name": "大纲分析", "icon": "fas fa-project-diagram"},
    {"key": "popular_tropes", "name": "热梗统计", "icon": "fas fa-fire-alt"}
]

# File upload settings
UPLOAD_FOLDER = os.getenv("UPLOAD_FOLDER", "uploads")
ALLOWED_EXTENSIONS = {"txt", "pdf", "docx", "epub"}
MAX_CONTENT_LENGTH = int(os.getenv("MAX_CONTENT_LENGTH", "50")) * 1024 * 1024  # 50MB
